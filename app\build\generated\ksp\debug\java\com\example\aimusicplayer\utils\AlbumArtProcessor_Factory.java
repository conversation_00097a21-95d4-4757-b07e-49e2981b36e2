package com.example.aimusicplayer.utils;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AlbumArtProcessor_Factory implements Factory<AlbumArtProcessor> {
  @Override
  public AlbumArtProcessor get() {
    return newInstance();
  }

  public static AlbumArtProcessor_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AlbumArtProcessor newInstance() {
    return new AlbumArtProcessor();
  }

  private static final class InstanceHolder {
    private static final AlbumArtProcessor_Factory INSTANCE = new AlbumArtProcessor_Factory();
  }
}
