package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.data.source.MusicDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicRepository_Factory implements Factory<MusicRepository> {
  private final Provider<MusicDataSource> musicDataSourceProvider;

  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  public MusicRepository_Factory(Provider<MusicDataSource> musicDataSourceProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    this.musicDataSourceProvider = musicDataSourceProvider;
    this.apiCacheManagerProvider = apiCacheManagerProvider;
  }

  @Override
  public MusicRepository get() {
    MusicRepository instance = newInstance(musicDataSourceProvider.get());
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
    return instance;
  }

  public static MusicRepository_Factory create(Provider<MusicDataSource> musicDataSourceProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    return new MusicRepository_Factory(musicDataSourceProvider, apiCacheManagerProvider);
  }

  public static MusicRepository newInstance(MusicDataSource musicDataSource) {
    return new MusicRepository(musicDataSource);
  }
}
