package com.example.aimusicplayer.ui.playlist

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Long
import kotlin.jvm.JvmStatic

public data class PlaylistDetailFragmentArgs(
  public val playlistId: Long,
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putLong("playlistId", this.playlistId)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("playlistId", this.playlistId)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): PlaylistDetailFragmentArgs {
      bundle.setClassLoader(PlaylistDetailFragmentArgs::class.java.classLoader)
      val __playlistId : Long
      if (bundle.containsKey("playlistId")) {
        __playlistId = bundle.getLong("playlistId")
      } else {
        throw IllegalArgumentException("Required argument \"playlistId\" is missing and does not have an android:defaultValue")
      }
      return PlaylistDetailFragmentArgs(__playlistId)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        PlaylistDetailFragmentArgs {
      val __playlistId : Long?
      if (savedStateHandle.contains("playlistId")) {
        __playlistId = savedStateHandle["playlistId"]
        if (__playlistId == null) {
          throw IllegalArgumentException("Argument \"playlistId\" of type long does not support null values")
        }
      } else {
        throw IllegalArgumentException("Required argument \"playlistId\" is missing and does not have an android:defaultValue")
      }
      return PlaylistDetailFragmentArgs(__playlistId)
    }
  }
}
