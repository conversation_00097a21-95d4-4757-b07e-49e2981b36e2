<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="page_player_comments" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\page_player_comments.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/page_player_comments_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="14"/></Target><Target id="@+id/text_comment_title" view="TextView"><Expressions/><location startLine="12" startOffset="8" endLine="19" endOffset="38"/></Target><Target id="@+id/text_comment_count" view="TextView"><Expressions/><location startLine="21" startOffset="8" endLine="29" endOffset="32"/></Target><Target id="@+id/recycler_view_comment" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="33" startOffset="4" endLine="39" endOffset="39"/></Target><Target id="@+id/edit_text_comment" view="EditText"><Expressions/><location startLine="51" startOffset="8" endLine="61" endOffset="34"/></Target><Target id="@+id/btn_send_comment" view="Button"><Expressions/><location startLine="63" startOffset="8" endLine="70" endOffset="59"/></Target></Targets></Layout>