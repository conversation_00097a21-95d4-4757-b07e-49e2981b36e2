// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemReplyBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CircleImageView imageAvatar;

  @NonNull
  public final ImageView imageReplyLike;

  @NonNull
  public final LinearLayout layoutReplyLike;

  @NonNull
  public final TextView textReplyContent;

  @NonNull
  public final TextView textReplyLikeCount;

  @NonNull
  public final TextView textReplyTime;

  @NonNull
  public final TextView textReplyToReply;

  @NonNull
  public final TextView textUsername;

  private ItemReplyBinding(@NonNull ConstraintLayout rootView, @NonNull CircleImageView imageAvatar,
      @NonNull ImageView imageReplyLike, @NonNull LinearLayout layoutReplyLike,
      @NonNull TextView textReplyContent, @NonNull TextView textReplyLikeCount,
      @NonNull TextView textReplyTime, @NonNull TextView textReplyToReply,
      @NonNull TextView textUsername) {
    this.rootView = rootView;
    this.imageAvatar = imageAvatar;
    this.imageReplyLike = imageReplyLike;
    this.layoutReplyLike = layoutReplyLike;
    this.textReplyContent = textReplyContent;
    this.textReplyLikeCount = textReplyLikeCount;
    this.textReplyTime = textReplyTime;
    this.textReplyToReply = textReplyToReply;
    this.textUsername = textUsername;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemReplyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemReplyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_reply, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemReplyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.image_avatar;
      CircleImageView imageAvatar = ViewBindings.findChildViewById(rootView, id);
      if (imageAvatar == null) {
        break missingId;
      }

      id = R.id.image_reply_like;
      ImageView imageReplyLike = ViewBindings.findChildViewById(rootView, id);
      if (imageReplyLike == null) {
        break missingId;
      }

      id = R.id.layout_reply_like;
      LinearLayout layoutReplyLike = ViewBindings.findChildViewById(rootView, id);
      if (layoutReplyLike == null) {
        break missingId;
      }

      id = R.id.text_reply_content;
      TextView textReplyContent = ViewBindings.findChildViewById(rootView, id);
      if (textReplyContent == null) {
        break missingId;
      }

      id = R.id.text_reply_like_count;
      TextView textReplyLikeCount = ViewBindings.findChildViewById(rootView, id);
      if (textReplyLikeCount == null) {
        break missingId;
      }

      id = R.id.text_reply_time;
      TextView textReplyTime = ViewBindings.findChildViewById(rootView, id);
      if (textReplyTime == null) {
        break missingId;
      }

      id = R.id.text_reply_to_reply;
      TextView textReplyToReply = ViewBindings.findChildViewById(rootView, id);
      if (textReplyToReply == null) {
        break missingId;
      }

      id = R.id.text_username;
      TextView textUsername = ViewBindings.findChildViewById(rootView, id);
      if (textUsername == null) {
        break missingId;
      }

      return new ItemReplyBinding((ConstraintLayout) rootView, imageAvatar, imageReplyLike,
          layoutReplyLike, textReplyContent, textReplyLikeCount, textReplyTime, textReplyToReply,
          textUsername);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
