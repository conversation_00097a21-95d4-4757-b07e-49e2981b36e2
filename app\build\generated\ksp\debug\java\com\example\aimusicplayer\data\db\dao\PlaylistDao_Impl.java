package com.example.aimusicplayer.data.db.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aimusicplayer.data.db.entity.PlaylistEntity;
import com.example.aimusicplayer.data.db.entity.PlaylistSongCrossRef;
import com.example.aimusicplayer.data.db.entity.SongEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PlaylistDao_Impl implements PlaylistDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PlaylistEntity> __insertionAdapterOfPlaylistEntity;

  private final EntityInsertionAdapter<PlaylistSongCrossRef> __insertionAdapterOfPlaylistSongCrossRef;

  private final EntityDeletionOrUpdateAdapter<PlaylistEntity> __deletionAdapterOfPlaylistEntity;

  private final EntityDeletionOrUpdateAdapter<PlaylistEntity> __updateAdapterOfPlaylistEntity;

  private final EntityDeletionOrUpdateAdapter<PlaylistSongCrossRef> __updateAdapterOfPlaylistSongCrossRef;

  private final SharedSQLiteStatement __preparedStmtOfRemoveSongFromPlaylist;

  private final SharedSQLiteStatement __preparedStmtOfClearPlaylist;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  public PlaylistDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPlaylistEntity = new EntityInsertionAdapter<PlaylistEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `playlists` (`playlist_id`,`name`,`cover_url`,`description`,`creator_id`,`creator_name`,`song_count`,`play_count`,`is_subscribed`,`create_time`,`update_time`,`is_local`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlaylistEntity entity) {
        statement.bindLong(1, entity.getPlaylistId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getCoverUrl());
        statement.bindString(4, entity.getDescription());
        statement.bindLong(5, entity.getCreatorId());
        statement.bindString(6, entity.getCreatorName());
        statement.bindLong(7, entity.getSongCount());
        statement.bindLong(8, entity.getPlayCount());
        final int _tmp = entity.isSubscribed() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getCreateTime());
        statement.bindLong(11, entity.getUpdateTime());
        final int _tmp_1 = entity.isLocal() ? 1 : 0;
        statement.bindLong(12, _tmp_1);
      }
    };
    this.__insertionAdapterOfPlaylistSongCrossRef = new EntityInsertionAdapter<PlaylistSongCrossRef>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `playlist_song_cross_ref` (`playlist_id`,`song_unique_id`,`sort_order`,`add_time`) VALUES (?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlaylistSongCrossRef entity) {
        statement.bindLong(1, entity.getPlaylistId());
        statement.bindString(2, entity.getSongUniqueId());
        statement.bindLong(3, entity.getSortOrder());
        statement.bindLong(4, entity.getAddTime());
      }
    };
    this.__deletionAdapterOfPlaylistEntity = new EntityDeletionOrUpdateAdapter<PlaylistEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `playlists` WHERE `playlist_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlaylistEntity entity) {
        statement.bindLong(1, entity.getPlaylistId());
      }
    };
    this.__updateAdapterOfPlaylistEntity = new EntityDeletionOrUpdateAdapter<PlaylistEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `playlists` SET `playlist_id` = ?,`name` = ?,`cover_url` = ?,`description` = ?,`creator_id` = ?,`creator_name` = ?,`song_count` = ?,`play_count` = ?,`is_subscribed` = ?,`create_time` = ?,`update_time` = ?,`is_local` = ? WHERE `playlist_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlaylistEntity entity) {
        statement.bindLong(1, entity.getPlaylistId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getCoverUrl());
        statement.bindString(4, entity.getDescription());
        statement.bindLong(5, entity.getCreatorId());
        statement.bindString(6, entity.getCreatorName());
        statement.bindLong(7, entity.getSongCount());
        statement.bindLong(8, entity.getPlayCount());
        final int _tmp = entity.isSubscribed() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getCreateTime());
        statement.bindLong(11, entity.getUpdateTime());
        final int _tmp_1 = entity.isLocal() ? 1 : 0;
        statement.bindLong(12, _tmp_1);
        statement.bindLong(13, entity.getPlaylistId());
      }
    };
    this.__updateAdapterOfPlaylistSongCrossRef = new EntityDeletionOrUpdateAdapter<PlaylistSongCrossRef>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `playlist_song_cross_ref` SET `playlist_id` = ?,`song_unique_id` = ?,`sort_order` = ?,`add_time` = ? WHERE `playlist_id` = ? AND `song_unique_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlaylistSongCrossRef entity) {
        statement.bindLong(1, entity.getPlaylistId());
        statement.bindString(2, entity.getSongUniqueId());
        statement.bindLong(3, entity.getSortOrder());
        statement.bindLong(4, entity.getAddTime());
        statement.bindLong(5, entity.getPlaylistId());
        statement.bindString(6, entity.getSongUniqueId());
      }
    };
    this.__preparedStmtOfRemoveSongFromPlaylist = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM playlist_song_cross_ref WHERE playlist_id = ? AND song_unique_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearPlaylist = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM playlist_song_cross_ref WHERE playlist_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM playlists";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final PlaylistEntity playlist,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPlaylistEntity.insert(playlist);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAll(final List<PlaylistEntity> playlists,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPlaylistEntity.insert(playlists);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object addSongToPlaylist(final PlaylistSongCrossRef crossRef,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPlaylistSongCrossRef.insert(crossRef);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object addSongsToPlaylist(final List<PlaylistSongCrossRef> crossRefs,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPlaylistSongCrossRef.insert(crossRefs);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final PlaylistEntity playlist,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPlaylistEntity.handle(playlist);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final PlaylistEntity playlist,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPlaylistEntity.handle(playlist);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSongOrder(final PlaylistSongCrossRef crossRef,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPlaylistSongCrossRef.handle(crossRef);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSongOrders(final List<PlaylistSongCrossRef> crossRefs,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPlaylistSongCrossRef.handleMultiple(crossRefs);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object removeSongFromPlaylist(final long playlistId, final String songUniqueId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRemoveSongFromPlaylist.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, playlistId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, songUniqueId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRemoveSongFromPlaylist.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearPlaylist(final long playlistId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearPlaylist.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, playlistId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearPlaylist.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getPlaylistById(final long playlistId,
      final Continuation<? super PlaylistEntity> $completion) {
    final String _sql = "SELECT * FROM playlists WHERE playlist_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, playlistId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PlaylistEntity>() {
      @Override
      @Nullable
      public PlaylistEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPlaylistId = CursorUtil.getColumnIndexOrThrow(_cursor, "playlist_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_url");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatorId = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_id");
          final int _cursorIndexOfCreatorName = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_name");
          final int _cursorIndexOfSongCount = CursorUtil.getColumnIndexOrThrow(_cursor, "song_count");
          final int _cursorIndexOfPlayCount = CursorUtil.getColumnIndexOrThrow(_cursor, "play_count");
          final int _cursorIndexOfIsSubscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_subscribed");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfIsLocal = CursorUtil.getColumnIndexOrThrow(_cursor, "is_local");
          final PlaylistEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpPlaylistId;
            _tmpPlaylistId = _cursor.getLong(_cursorIndexOfPlaylistId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final long _tmpCreatorId;
            _tmpCreatorId = _cursor.getLong(_cursorIndexOfCreatorId);
            final String _tmpCreatorName;
            _tmpCreatorName = _cursor.getString(_cursorIndexOfCreatorName);
            final int _tmpSongCount;
            _tmpSongCount = _cursor.getInt(_cursorIndexOfSongCount);
            final int _tmpPlayCount;
            _tmpPlayCount = _cursor.getInt(_cursorIndexOfPlayCount);
            final boolean _tmpIsSubscribed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscribed);
            _tmpIsSubscribed = _tmp != 0;
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            final boolean _tmpIsLocal;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsLocal);
            _tmpIsLocal = _tmp_1 != 0;
            _result = new PlaylistEntity(_tmpPlaylistId,_tmpName,_tmpCoverUrl,_tmpDescription,_tmpCreatorId,_tmpCreatorName,_tmpSongCount,_tmpPlayCount,_tmpIsSubscribed,_tmpCreateTime,_tmpUpdateTime,_tmpIsLocal);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PlaylistEntity>> getAllPlaylists() {
    final String _sql = "SELECT * FROM playlists";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"playlists"}, new Callable<List<PlaylistEntity>>() {
      @Override
      @NonNull
      public List<PlaylistEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPlaylistId = CursorUtil.getColumnIndexOrThrow(_cursor, "playlist_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_url");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatorId = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_id");
          final int _cursorIndexOfCreatorName = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_name");
          final int _cursorIndexOfSongCount = CursorUtil.getColumnIndexOrThrow(_cursor, "song_count");
          final int _cursorIndexOfPlayCount = CursorUtil.getColumnIndexOrThrow(_cursor, "play_count");
          final int _cursorIndexOfIsSubscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_subscribed");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfIsLocal = CursorUtil.getColumnIndexOrThrow(_cursor, "is_local");
          final List<PlaylistEntity> _result = new ArrayList<PlaylistEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PlaylistEntity _item;
            final long _tmpPlaylistId;
            _tmpPlaylistId = _cursor.getLong(_cursorIndexOfPlaylistId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final long _tmpCreatorId;
            _tmpCreatorId = _cursor.getLong(_cursorIndexOfCreatorId);
            final String _tmpCreatorName;
            _tmpCreatorName = _cursor.getString(_cursorIndexOfCreatorName);
            final int _tmpSongCount;
            _tmpSongCount = _cursor.getInt(_cursorIndexOfSongCount);
            final int _tmpPlayCount;
            _tmpPlayCount = _cursor.getInt(_cursorIndexOfPlayCount);
            final boolean _tmpIsSubscribed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscribed);
            _tmpIsSubscribed = _tmp != 0;
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            final boolean _tmpIsLocal;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsLocal);
            _tmpIsLocal = _tmp_1 != 0;
            _item = new PlaylistEntity(_tmpPlaylistId,_tmpName,_tmpCoverUrl,_tmpDescription,_tmpCreatorId,_tmpCreatorName,_tmpSongCount,_tmpPlayCount,_tmpIsSubscribed,_tmpCreateTime,_tmpUpdateTime,_tmpIsLocal);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PlaylistEntity>> getAllLocalPlaylists() {
    final String _sql = "SELECT * FROM playlists WHERE is_local = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"playlists"}, new Callable<List<PlaylistEntity>>() {
      @Override
      @NonNull
      public List<PlaylistEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPlaylistId = CursorUtil.getColumnIndexOrThrow(_cursor, "playlist_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_url");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatorId = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_id");
          final int _cursorIndexOfCreatorName = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_name");
          final int _cursorIndexOfSongCount = CursorUtil.getColumnIndexOrThrow(_cursor, "song_count");
          final int _cursorIndexOfPlayCount = CursorUtil.getColumnIndexOrThrow(_cursor, "play_count");
          final int _cursorIndexOfIsSubscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_subscribed");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfIsLocal = CursorUtil.getColumnIndexOrThrow(_cursor, "is_local");
          final List<PlaylistEntity> _result = new ArrayList<PlaylistEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PlaylistEntity _item;
            final long _tmpPlaylistId;
            _tmpPlaylistId = _cursor.getLong(_cursorIndexOfPlaylistId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final long _tmpCreatorId;
            _tmpCreatorId = _cursor.getLong(_cursorIndexOfCreatorId);
            final String _tmpCreatorName;
            _tmpCreatorName = _cursor.getString(_cursorIndexOfCreatorName);
            final int _tmpSongCount;
            _tmpSongCount = _cursor.getInt(_cursorIndexOfSongCount);
            final int _tmpPlayCount;
            _tmpPlayCount = _cursor.getInt(_cursorIndexOfPlayCount);
            final boolean _tmpIsSubscribed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscribed);
            _tmpIsSubscribed = _tmp != 0;
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            final boolean _tmpIsLocal;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsLocal);
            _tmpIsLocal = _tmp_1 != 0;
            _item = new PlaylistEntity(_tmpPlaylistId,_tmpName,_tmpCoverUrl,_tmpDescription,_tmpCreatorId,_tmpCreatorName,_tmpSongCount,_tmpPlayCount,_tmpIsSubscribed,_tmpCreateTime,_tmpUpdateTime,_tmpIsLocal);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PlaylistEntity>> getAllOnlinePlaylists() {
    final String _sql = "SELECT * FROM playlists WHERE is_local = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"playlists"}, new Callable<List<PlaylistEntity>>() {
      @Override
      @NonNull
      public List<PlaylistEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPlaylistId = CursorUtil.getColumnIndexOrThrow(_cursor, "playlist_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_url");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatorId = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_id");
          final int _cursorIndexOfCreatorName = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_name");
          final int _cursorIndexOfSongCount = CursorUtil.getColumnIndexOrThrow(_cursor, "song_count");
          final int _cursorIndexOfPlayCount = CursorUtil.getColumnIndexOrThrow(_cursor, "play_count");
          final int _cursorIndexOfIsSubscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_subscribed");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfIsLocal = CursorUtil.getColumnIndexOrThrow(_cursor, "is_local");
          final List<PlaylistEntity> _result = new ArrayList<PlaylistEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PlaylistEntity _item;
            final long _tmpPlaylistId;
            _tmpPlaylistId = _cursor.getLong(_cursorIndexOfPlaylistId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final long _tmpCreatorId;
            _tmpCreatorId = _cursor.getLong(_cursorIndexOfCreatorId);
            final String _tmpCreatorName;
            _tmpCreatorName = _cursor.getString(_cursorIndexOfCreatorName);
            final int _tmpSongCount;
            _tmpSongCount = _cursor.getInt(_cursorIndexOfSongCount);
            final int _tmpPlayCount;
            _tmpPlayCount = _cursor.getInt(_cursorIndexOfPlayCount);
            final boolean _tmpIsSubscribed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscribed);
            _tmpIsSubscribed = _tmp != 0;
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            final boolean _tmpIsLocal;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsLocal);
            _tmpIsLocal = _tmp_1 != 0;
            _item = new PlaylistEntity(_tmpPlaylistId,_tmpName,_tmpCoverUrl,_tmpDescription,_tmpCreatorId,_tmpCreatorName,_tmpSongCount,_tmpPlayCount,_tmpIsSubscribed,_tmpCreateTime,_tmpUpdateTime,_tmpIsLocal);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PlaylistEntity>> getAllSubscribedPlaylists() {
    final String _sql = "SELECT * FROM playlists WHERE is_subscribed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"playlists"}, new Callable<List<PlaylistEntity>>() {
      @Override
      @NonNull
      public List<PlaylistEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPlaylistId = CursorUtil.getColumnIndexOrThrow(_cursor, "playlist_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_url");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatorId = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_id");
          final int _cursorIndexOfCreatorName = CursorUtil.getColumnIndexOrThrow(_cursor, "creator_name");
          final int _cursorIndexOfSongCount = CursorUtil.getColumnIndexOrThrow(_cursor, "song_count");
          final int _cursorIndexOfPlayCount = CursorUtil.getColumnIndexOrThrow(_cursor, "play_count");
          final int _cursorIndexOfIsSubscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_subscribed");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfIsLocal = CursorUtil.getColumnIndexOrThrow(_cursor, "is_local");
          final List<PlaylistEntity> _result = new ArrayList<PlaylistEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PlaylistEntity _item;
            final long _tmpPlaylistId;
            _tmpPlaylistId = _cursor.getLong(_cursorIndexOfPlaylistId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final long _tmpCreatorId;
            _tmpCreatorId = _cursor.getLong(_cursorIndexOfCreatorId);
            final String _tmpCreatorName;
            _tmpCreatorName = _cursor.getString(_cursorIndexOfCreatorName);
            final int _tmpSongCount;
            _tmpSongCount = _cursor.getInt(_cursorIndexOfSongCount);
            final int _tmpPlayCount;
            _tmpPlayCount = _cursor.getInt(_cursorIndexOfPlayCount);
            final boolean _tmpIsSubscribed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscribed);
            _tmpIsSubscribed = _tmp != 0;
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            final boolean _tmpIsLocal;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsLocal);
            _tmpIsLocal = _tmp_1 != 0;
            _item = new PlaylistEntity(_tmpPlaylistId,_tmpName,_tmpCoverUrl,_tmpDescription,_tmpCreatorId,_tmpCreatorName,_tmpSongCount,_tmpPlayCount,_tmpIsSubscribed,_tmpCreateTime,_tmpUpdateTime,_tmpIsLocal);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SongEntity>> getPlaylistSongs(final long playlistId) {
    final String _sql = "SELECT s.* FROM songs s INNER JOIN playlist_song_cross_ref ref ON s.unique_id = ref.song_unique_id WHERE ref.playlist_id = ? ORDER BY ref.sort_order";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, playlistId);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"songs",
        "playlist_song_cross_ref"}, new Callable<List<SongEntity>>() {
      @Override
      @NonNull
      public List<SongEntity> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
          try {
            final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
            final int _cursorIndexOfSongId = CursorUtil.getColumnIndexOrThrow(_cursor, "song_id");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfArtist = CursorUtil.getColumnIndexOrThrow(_cursor, "artist");
            final int _cursorIndexOfArtistId = CursorUtil.getColumnIndexOrThrow(_cursor, "artist_id");
            final int _cursorIndexOfAlbum = CursorUtil.getColumnIndexOrThrow(_cursor, "album");
            final int _cursorIndexOfAlbumId = CursorUtil.getColumnIndexOrThrow(_cursor, "album_id");
            final int _cursorIndexOfAlbumCover = CursorUtil.getColumnIndexOrThrow(_cursor, "album_cover");
            final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
            final int _cursorIndexOfUri = CursorUtil.getColumnIndexOrThrow(_cursor, "uri");
            final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
            final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "file_name");
            final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "file_size");
            final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "is_vip");
            final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "is_favorite");
            final int _cursorIndexOfLastPlayedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "last_played_time");
            final int _cursorIndexOfUniqueId = CursorUtil.getColumnIndexOrThrow(_cursor, "unique_id");
            final List<SongEntity> _result = new ArrayList<SongEntity>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final SongEntity _item;
              final int _tmpType;
              _tmpType = _cursor.getInt(_cursorIndexOfType);
              final long _tmpSongId;
              _tmpSongId = _cursor.getLong(_cursorIndexOfSongId);
              final String _tmpTitle;
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              final String _tmpArtist;
              _tmpArtist = _cursor.getString(_cursorIndexOfArtist);
              final long _tmpArtistId;
              _tmpArtistId = _cursor.getLong(_cursorIndexOfArtistId);
              final String _tmpAlbum;
              _tmpAlbum = _cursor.getString(_cursorIndexOfAlbum);
              final long _tmpAlbumId;
              _tmpAlbumId = _cursor.getLong(_cursorIndexOfAlbumId);
              final String _tmpAlbumCover;
              _tmpAlbumCover = _cursor.getString(_cursorIndexOfAlbumCover);
              final long _tmpDuration;
              _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
              final String _tmpUri;
              _tmpUri = _cursor.getString(_cursorIndexOfUri);
              final String _tmpPath;
              _tmpPath = _cursor.getString(_cursorIndexOfPath);
              final String _tmpFileName;
              _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
              final long _tmpFileSize;
              _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
              final boolean _tmpIsVip;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsVip);
              _tmpIsVip = _tmp != 0;
              final boolean _tmpIsFavorite;
              final int _tmp_1;
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsFavorite);
              _tmpIsFavorite = _tmp_1 != 0;
              final long _tmpLastPlayedTime;
              _tmpLastPlayedTime = _cursor.getLong(_cursorIndexOfLastPlayedTime);
              _item = new SongEntity(_tmpType,_tmpSongId,_tmpTitle,_tmpArtist,_tmpArtistId,_tmpAlbum,_tmpAlbumId,_tmpAlbumCover,_tmpDuration,_tmpUri,_tmpPath,_tmpFileName,_tmpFileSize,_tmpIsVip,_tmpIsFavorite,_tmpLastPlayedTime);
              final String _tmpUniqueId;
              _tmpUniqueId = _cursor.getString(_cursorIndexOfUniqueId);
              _item.setUniqueId(_tmpUniqueId);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
