<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_top_list" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_top_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_top_list_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="57" endOffset="35"/></Target><Target id="@+id/topListCoverImageView" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="44"/></Target><Target id="@+id/topListNameTextView" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="36" endOffset="42"/></Target><Target id="@+id/topListDescTextView" view="TextView"><Expressions/><location startLine="38" startOffset="12" endLine="46" endOffset="41"/></Target><Target id="@+id/topListSongCountTextView" view="TextView"><Expressions/><location startLine="48" startOffset="12" endLine="54" endOffset="41"/></Target></Targets></Layout>