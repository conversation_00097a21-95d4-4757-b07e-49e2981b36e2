package com.example.aimusicplayer.ui.search

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import kotlin.String
import kotlin.jvm.JvmStatic

public data class SearchFragmentArgs(
  public val keyword: String? = null,
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putString("keyword", this.keyword)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("keyword", this.keyword)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): SearchFragmentArgs {
      bundle.setClassLoader(SearchFragmentArgs::class.java.classLoader)
      val __keyword : String?
      if (bundle.containsKey("keyword")) {
        __keyword = bundle.getString("keyword")
      } else {
        __keyword = null
      }
      return SearchFragmentArgs(__keyword)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle): SearchFragmentArgs {
      val __keyword : String?
      if (savedStateHandle.contains("keyword")) {
        __keyword = savedStateHandle["keyword"]
      } else {
        __keyword = null
      }
      return SearchFragmentArgs(__keyword)
    }
  }
}
