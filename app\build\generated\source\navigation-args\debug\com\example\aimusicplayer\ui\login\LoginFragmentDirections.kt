package com.example.aimusicplayer.ui.login

import android.os.Bundle
import androidx.navigation.NavDirections
import com.example.aimusicplayer.R
import kotlin.Int
import kotlin.Long

public class LoginFragmentDirections private constructor() {
  private data class ActionLoginFragmentToPlayerFragment(
    public val songId: Long = -1L,
    public val playlistId: Long = -1L,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_loginFragment_to_playerFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("songId", this.songId)
        result.putLong("playlistId", this.playlistId)
        return result
      }
  }

  public companion object {
    public fun actionLoginFragmentToPlayerFragment(songId: Long = -1L, playlistId: Long = -1L):
        NavDirections = ActionLoginFragmentToPlayerFragment(songId, playlistId)
  }
}
