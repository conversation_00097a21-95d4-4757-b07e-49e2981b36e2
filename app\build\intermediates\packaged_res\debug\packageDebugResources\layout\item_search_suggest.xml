<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="12dp"
    android:paddingVertical="10dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:id="@+id/typeIconView"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="4dp"
        android:contentDescription="类型图标"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@android:drawable/ic_media_play" />

    <TextView
        android:id="@+id/suggestTitleTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="@color/text_primary_color"
        android:textSize="15sp"
        android:singleLine="true"
        android:ellipsize="end"
        app:layout_constraintBottom_toTopOf="@+id/suggestSubTextView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/typeIconView"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="搜索建议标题" />

    <TextView
        android:id="@+id/suggestSubTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="@color/text_secondary_color"
        android:textSize="12sp"
        android:singleLine="true"
        android:ellipsize="end"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/typeIconView"
        app:layout_constraintTop_toBottomOf="@+id/suggestTitleTextView"
        tools:text="歌手名 - 专辑名"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout> 