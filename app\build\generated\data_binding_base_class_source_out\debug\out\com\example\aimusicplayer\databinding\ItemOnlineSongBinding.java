// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemOnlineSongBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView itemAlbumName;

  @NonNull
  public final TextView itemArtistName;

  @NonNull
  public final TextView itemSongName;

  @NonNull
  public final TextView itemVipTag;

  private ItemOnlineSongBinding(@NonNull CardView rootView, @NonNull TextView itemAlbumName,
      @NonNull TextView itemArtistName, @NonNull TextView itemSongName,
      @NonNull TextView itemVipTag) {
    this.rootView = rootView;
    this.itemAlbumName = itemAlbumName;
    this.itemArtistName = itemArtistName;
    this.itemSongName = itemSongName;
    this.itemVipTag = itemVipTag;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemOnlineSongBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemOnlineSongBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_online_song, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemOnlineSongBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.item_album_name;
      TextView itemAlbumName = ViewBindings.findChildViewById(rootView, id);
      if (itemAlbumName == null) {
        break missingId;
      }

      id = R.id.item_artist_name;
      TextView itemArtistName = ViewBindings.findChildViewById(rootView, id);
      if (itemArtistName == null) {
        break missingId;
      }

      id = R.id.item_song_name;
      TextView itemSongName = ViewBindings.findChildViewById(rootView, id);
      if (itemSongName == null) {
        break missingId;
      }

      id = R.id.item_vip_tag;
      TextView itemVipTag = ViewBindings.findChildViewById(rootView, id);
      if (itemVipTag == null) {
        break missingId;
      }

      return new ItemOnlineSongBinding((CardView) rootView, itemAlbumName, itemArtistName,
          itemSongName, itemVipTag);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
