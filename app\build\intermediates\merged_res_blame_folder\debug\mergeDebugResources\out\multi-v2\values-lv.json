{"logs": [{"outputFile": "com.example.aimusicplayer.app-mergeDebugResources-60:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcdce9d9bb74ce2c916a6bbd71ef776f\\transformed\\media3-exoplayer-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7018,7093,7159,7231,7301,7381,7458,7559,7657", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "7088,7154,7226,7296,7376,7453,7554,7652,7731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92121f0061d84fd6603428dd9555221c\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1150,1270,1380,1489,1575,1679,1801,1883,1963,2073,2181,2287,2396,2507,2610,2722,2829,2934,3034,3119,3228,3339,3438,3549,3656,3761,3935,14371", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "1265,1375,1484,1570,1674,1796,1878,1958,2068,2176,2282,2391,2502,2605,2717,2824,2929,3029,3114,3223,3334,3433,3544,3651,3756,3930,4029,14449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1916aa273d557d541d17bc9f12ca7920\\transformed\\media3-session-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,136,212,282,352,433,520,614", "endColumns": "80,75,69,69,80,86,93,102", "endOffsets": "131,207,277,347,428,515,609,712"}, "to": {"startLines": "53,62,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4034,4879,10103,10173,10243,10324,10411,10505", "endColumns": "80,75,69,69,80,86,93,102", "endOffsets": "4110,4950,10168,10238,10319,10406,10500,10603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f4bdb46ff28ab587a34c53d6687bf99\\transformed\\navigation-ui-2.7.5\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "14057,14171", "endColumns": "113,122", "endOffsets": "14166,14289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\83271d0c85512209c52890db5dc246bd\\transformed\\material-1.11.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1153,1247,1320,1381,1506,1572,1640,1701,1773,1833,1887,2007,2067,2129,2183,2260,2390,2477,2559,2700,2780,2865,2992,3083,3159,3213,3266,3332,3406,3487,3571,3651,3724,3801,3878,3952,4062,4155,4230,4320,4411,4483,4561,4652,4706,4789,4857,4941,5028,5090,5154,5217,5289,5399,5512,5615,5724,5782,5839", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,126,90,75,53,52,65,73,80,83,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1148,1242,1315,1376,1501,1567,1635,1696,1768,1828,1882,2002,2062,2124,2178,2255,2385,2472,2554,2695,2775,2860,2987,3078,3154,3208,3261,3327,3401,3482,3566,3646,3719,3796,3873,3947,4057,4150,4225,4315,4406,4478,4556,4647,4701,4784,4852,4936,5023,5085,5149,5212,5284,5394,5507,5610,5719,5777,5834,5911"}, "to": {"startLines": "21,54,55,56,57,58,59,60,61,63,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,4115,4202,4287,4368,4473,4561,4662,4796,4955,8866,8960,9033,9094,9219,9285,9353,9414,9486,9546,9600,9720,9780,9842,9896,9973,10608,10695,10777,10918,10998,11083,11210,11301,11377,11431,11484,11550,11624,11705,11789,11869,11942,12019,12096,12170,12280,12373,12448,12538,12629,12701,12779,12870,12924,13007,13075,13159,13246,13308,13372,13435,13507,13617,13730,13833,13942,14000,14294", "endLines": "25,54,55,56,57,58,59,60,61,63,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,126,90,75,53,52,65,73,80,83,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76", "endOffsets": "1145,4197,4282,4363,4468,4556,4657,4791,4874,5015,8955,9028,9089,9214,9280,9348,9409,9481,9541,9595,9715,9775,9837,9891,9968,10098,10690,10772,10913,10993,11078,11205,11296,11372,11426,11479,11545,11619,11700,11784,11864,11937,12014,12091,12165,12275,12368,12443,12533,12624,12696,12774,12865,12919,13002,13070,13154,13241,13303,13367,13430,13502,13612,13725,13828,13937,13995,14052,14366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48fbfb4201531ba0d2c54a69b6a94add\\transformed\\core-1.9.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "183", "startColumns": "4", "startOffsets": "14454", "endColumns": "100", "endOffsets": "14550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78bf313ee09e26c8c671a0bbc2457ec1\\transformed\\media3-ui-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1977,2088,2195,2269,2351,2425,2498,2598,2697,2763,2829,2882,2940,2988,3049,3107,3183,3247,3312,3377,3434,3500,3566,3632,3684,3748,3826,3904", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1972,2083,2190,2264,2346,2420,2493,2593,2692,2758,2824,2877,2935,2983,3044,3102,3178,3242,3307,3372,3429,3495,3561,3627,3679,3743,3821,3899,3954"}, "to": {"startLines": "2,11,16,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,662,5020,5104,5187,5270,5365,5460,5533,5600,5694,5788,5854,5921,5984,6060,6166,6277,6384,6458,6540,6614,6687,6787,6886,6952,7736,7789,7847,7895,7956,8014,8090,8154,8219,8284,8341,8407,8473,8539,8591,8655,8733,8811", "endLines": "10,15,20,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "376,657,921,5099,5182,5265,5360,5455,5528,5595,5689,5783,5849,5916,5979,6055,6161,6272,6379,6453,6535,6609,6682,6782,6881,6947,7013,7784,7842,7890,7951,8009,8085,8149,8214,8279,8336,8402,8468,8534,8586,8650,8728,8806,8861"}}]}]}