// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.google.android.material.card.MaterialCardView;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCommentBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CircleImageView imageCommentAvatar;

  @NonNull
  public final ImageView imageLike;

  @NonNull
  public final MaterialCardView layoutLike;

  @NonNull
  public final MaterialCardView layoutMore;

  @NonNull
  public final MaterialCardView layoutReply;

  @NonNull
  public final RecyclerView recyclerViewReplies;

  @NonNull
  public final TextView textCommentContent;

  @NonNull
  public final TextView textCommentTime;

  @NonNull
  public final TextView textCommentUsername;

  @NonNull
  public final TextView textLikeCount;

  @NonNull
  public final TextView textReplyCount;

  private ItemCommentBinding(@NonNull CardView rootView,
      @NonNull CircleImageView imageCommentAvatar, @NonNull ImageView imageLike,
      @NonNull MaterialCardView layoutLike, @NonNull MaterialCardView layoutMore,
      @NonNull MaterialCardView layoutReply, @NonNull RecyclerView recyclerViewReplies,
      @NonNull TextView textCommentContent, @NonNull TextView textCommentTime,
      @NonNull TextView textCommentUsername, @NonNull TextView textLikeCount,
      @NonNull TextView textReplyCount) {
    this.rootView = rootView;
    this.imageCommentAvatar = imageCommentAvatar;
    this.imageLike = imageLike;
    this.layoutLike = layoutLike;
    this.layoutMore = layoutMore;
    this.layoutReply = layoutReply;
    this.recyclerViewReplies = recyclerViewReplies;
    this.textCommentContent = textCommentContent;
    this.textCommentTime = textCommentTime;
    this.textCommentUsername = textCommentUsername;
    this.textLikeCount = textLikeCount;
    this.textReplyCount = textReplyCount;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCommentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCommentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_comment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCommentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.image_comment_avatar;
      CircleImageView imageCommentAvatar = ViewBindings.findChildViewById(rootView, id);
      if (imageCommentAvatar == null) {
        break missingId;
      }

      id = R.id.image_like;
      ImageView imageLike = ViewBindings.findChildViewById(rootView, id);
      if (imageLike == null) {
        break missingId;
      }

      id = R.id.layout_like;
      MaterialCardView layoutLike = ViewBindings.findChildViewById(rootView, id);
      if (layoutLike == null) {
        break missingId;
      }

      id = R.id.layout_more;
      MaterialCardView layoutMore = ViewBindings.findChildViewById(rootView, id);
      if (layoutMore == null) {
        break missingId;
      }

      id = R.id.layout_reply;
      MaterialCardView layoutReply = ViewBindings.findChildViewById(rootView, id);
      if (layoutReply == null) {
        break missingId;
      }

      id = R.id.recycler_view_replies;
      RecyclerView recyclerViewReplies = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewReplies == null) {
        break missingId;
      }

      id = R.id.text_comment_content;
      TextView textCommentContent = ViewBindings.findChildViewById(rootView, id);
      if (textCommentContent == null) {
        break missingId;
      }

      id = R.id.text_comment_time;
      TextView textCommentTime = ViewBindings.findChildViewById(rootView, id);
      if (textCommentTime == null) {
        break missingId;
      }

      id = R.id.text_comment_username;
      TextView textCommentUsername = ViewBindings.findChildViewById(rootView, id);
      if (textCommentUsername == null) {
        break missingId;
      }

      id = R.id.text_like_count;
      TextView textLikeCount = ViewBindings.findChildViewById(rootView, id);
      if (textLikeCount == null) {
        break missingId;
      }

      id = R.id.text_reply_count;
      TextView textReplyCount = ViewBindings.findChildViewById(rootView, id);
      if (textReplyCount == null) {
        break missingId;
      }

      return new ItemCommentBinding((CardView) rootView, imageCommentAvatar, imageLike, layoutLike,
          layoutMore, layoutReply, recyclerViewReplies, textCommentContent, textCommentTime,
          textCommentUsername, textLikeCount, textReplyCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
