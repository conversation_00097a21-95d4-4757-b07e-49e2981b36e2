<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_main_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="221" endOffset="16"/></Target><Target id="@+id/nav_host_fragment" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="46"/></Target><Target id="@+id/fragment_container" view="FrameLayout"><Expressions/><location startLine="19" startOffset="4" endLine="23" endOffset="35"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="26" startOffset="4" endLine="36" endOffset="25"/></Target><Target id="@+id/sidebar_nav" view="LinearLayout"><Expressions/><location startLine="39" startOffset="4" endLine="206" endOffset="18"/></Target><Target id="@+id/nav_player" view="ImageView"><Expressions/><location startLine="65" startOffset="12" endLine="71" endOffset="50"/></Target><Target id="@+id/nav_player_indicator" view="View"><Expressions/><location startLine="73" startOffset="12" endLine="79" endOffset="56"/></Target><Target id="@+id/nav_library" view="ImageView"><Expressions/><location startLine="90" startOffset="12" endLine="96" endOffset="52"/></Target><Target id="@+id/nav_library_indicator" view="View"><Expressions/><location startLine="98" startOffset="12" endLine="104" endOffset="57"/></Target><Target id="@+id/nav_discovery" view="ImageView"><Expressions/><location startLine="115" startOffset="12" endLine="121" endOffset="51"/></Target><Target id="@+id/nav_discovery_indicator" view="View"><Expressions/><location startLine="123" startOffset="12" endLine="129" endOffset="59"/></Target><Target id="@+id/nav_driving" view="ImageView"><Expressions/><location startLine="140" startOffset="12" endLine="146" endOffset="51"/></Target><Target id="@+id/nav_driving_indicator" view="View"><Expressions/><location startLine="148" startOffset="12" endLine="154" endOffset="57"/></Target><Target id="@+id/nav_profile" view="ImageView"><Expressions/><location startLine="165" startOffset="12" endLine="171" endOffset="51"/></Target><Target id="@+id/nav_profile_indicator" view="View"><Expressions/><location startLine="173" startOffset="12" endLine="179" endOffset="57"/></Target><Target id="@+id/nav_settings" view="ImageView"><Expressions/><location startLine="190" startOffset="12" endLine="196" endOffset="49"/></Target><Target id="@+id/nav_settings_indicator" view="View"><Expressions/><location startLine="198" startOffset="12" endLine="204" endOffset="58"/></Target><Target id="@+id/btn_menu_right" view="ImageView"><Expressions/><location startLine="209" startOffset="4" endLine="220" endOffset="33"/></Target></Targets></Layout>