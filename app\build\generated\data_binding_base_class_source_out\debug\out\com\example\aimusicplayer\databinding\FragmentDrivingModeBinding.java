// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDrivingModeBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView albumArt;

  @NonNull
  public final TextView artistText;

  @NonNull
  public final TextView currentTime;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final ImageButton exitButton;

  @NonNull
  public final LinearLayout exitContainer;

  @NonNull
  public final LinearLayout headerContainer;

  @NonNull
  public final ProgressBar loadingProgress;

  @NonNull
  public final ImageButton nextButton;

  @NonNull
  public final ImageButton playPauseButton;

  @NonNull
  public final ImageButton previousButton;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final TextView songTitle;

  @NonNull
  public final TextView timeText;

  @NonNull
  public final TextView totalTime;

  @NonNull
  public final ImageButton voiceButton;

  @NonNull
  public final LinearLayout voiceControlContainer;

  private FragmentDrivingModeBinding(@NonNull RelativeLayout rootView, @NonNull ImageView albumArt,
      @NonNull TextView artistText, @NonNull TextView currentTime, @NonNull TextView dateText,
      @NonNull ImageButton exitButton, @NonNull LinearLayout exitContainer,
      @NonNull LinearLayout headerContainer, @NonNull ProgressBar loadingProgress,
      @NonNull ImageButton nextButton, @NonNull ImageButton playPauseButton,
      @NonNull ImageButton previousButton, @NonNull SeekBar seekBar, @NonNull TextView songTitle,
      @NonNull TextView timeText, @NonNull TextView totalTime, @NonNull ImageButton voiceButton,
      @NonNull LinearLayout voiceControlContainer) {
    this.rootView = rootView;
    this.albumArt = albumArt;
    this.artistText = artistText;
    this.currentTime = currentTime;
    this.dateText = dateText;
    this.exitButton = exitButton;
    this.exitContainer = exitContainer;
    this.headerContainer = headerContainer;
    this.loadingProgress = loadingProgress;
    this.nextButton = nextButton;
    this.playPauseButton = playPauseButton;
    this.previousButton = previousButton;
    this.seekBar = seekBar;
    this.songTitle = songTitle;
    this.timeText = timeText;
    this.totalTime = totalTime;
    this.voiceButton = voiceButton;
    this.voiceControlContainer = voiceControlContainer;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDrivingModeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDrivingModeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_driving_mode, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDrivingModeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.album_art;
      ImageView albumArt = ViewBindings.findChildViewById(rootView, id);
      if (albumArt == null) {
        break missingId;
      }

      id = R.id.artist_text;
      TextView artistText = ViewBindings.findChildViewById(rootView, id);
      if (artistText == null) {
        break missingId;
      }

      id = R.id.current_time;
      TextView currentTime = ViewBindings.findChildViewById(rootView, id);
      if (currentTime == null) {
        break missingId;
      }

      id = R.id.date_text;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.exit_button;
      ImageButton exitButton = ViewBindings.findChildViewById(rootView, id);
      if (exitButton == null) {
        break missingId;
      }

      id = R.id.exit_container;
      LinearLayout exitContainer = ViewBindings.findChildViewById(rootView, id);
      if (exitContainer == null) {
        break missingId;
      }

      id = R.id.header_container;
      LinearLayout headerContainer = ViewBindings.findChildViewById(rootView, id);
      if (headerContainer == null) {
        break missingId;
      }

      id = R.id.loading_progress;
      ProgressBar loadingProgress = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgress == null) {
        break missingId;
      }

      id = R.id.next_button;
      ImageButton nextButton = ViewBindings.findChildViewById(rootView, id);
      if (nextButton == null) {
        break missingId;
      }

      id = R.id.play_pause_button;
      ImageButton playPauseButton = ViewBindings.findChildViewById(rootView, id);
      if (playPauseButton == null) {
        break missingId;
      }

      id = R.id.previous_button;
      ImageButton previousButton = ViewBindings.findChildViewById(rootView, id);
      if (previousButton == null) {
        break missingId;
      }

      id = R.id.seek_bar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.song_title;
      TextView songTitle = ViewBindings.findChildViewById(rootView, id);
      if (songTitle == null) {
        break missingId;
      }

      id = R.id.time_text;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      id = R.id.total_time;
      TextView totalTime = ViewBindings.findChildViewById(rootView, id);
      if (totalTime == null) {
        break missingId;
      }

      id = R.id.voice_button;
      ImageButton voiceButton = ViewBindings.findChildViewById(rootView, id);
      if (voiceButton == null) {
        break missingId;
      }

      id = R.id.voice_control_container;
      LinearLayout voiceControlContainer = ViewBindings.findChildViewById(rootView, id);
      if (voiceControlContainer == null) {
        break missingId;
      }

      return new FragmentDrivingModeBinding((RelativeLayout) rootView, albumArt, artistText,
          currentTime, dateText, exitButton, exitContainer, headerContainer, loadingProgress,
          nextButton, playPauseButton, previousButton, seekBar, songTitle, timeText, totalTime,
          voiceButton, voiceControlContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
