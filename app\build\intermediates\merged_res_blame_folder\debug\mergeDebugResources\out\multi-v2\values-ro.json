{"logs": [{"outputFile": "com.example.aimusicplayer.app-mergeDebugResources-60:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\83271d0c85512209c52890db5dc246bd\\transformed\\material-1.11.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2963,3053,3133,3188,3239,3305,3378,3456,3544,3629,3700,3777,3851,3923,4029,4120,4194,4289,4387,4461,4541,4642,4695,4781,4847,4936,5026,5088,5152,5215,5289,5401,5511,5621,5726,5785,5840", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2958,3048,3128,3183,3234,3300,3373,3451,3539,3624,3695,3772,3846,3918,4024,4115,4189,4284,4382,4456,4536,4637,4690,4776,4842,4931,5021,5083,5147,5210,5284,5396,5506,5616,5721,5780,5835,5914"}, "to": {"startLines": "21,54,55,56,57,58,59,60,61,63,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,3998,4090,4178,4265,4361,4451,4552,4673,4832,8749,8844,8918,8978,9062,9124,9190,9248,9321,9384,9440,9559,9616,9677,9733,9807,10457,10543,10627,10760,10842,10925,11071,11161,11241,11296,11347,11413,11486,11564,11652,11737,11808,11885,11959,12031,12137,12228,12302,12397,12495,12569,12649,12750,12803,12889,12955,13044,13134,13196,13260,13323,13397,13509,13619,13729,13834,13893,14182", "endLines": "25,54,55,56,57,58,59,60,61,63,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "1163,4085,4173,4260,4356,4446,4547,4668,4752,4893,8839,8913,8973,9057,9119,9185,9243,9316,9379,9435,9554,9611,9672,9728,9802,9947,10538,10622,10755,10837,10920,11066,11156,11236,11291,11342,11408,11481,11559,11647,11732,11803,11880,11954,12026,12132,12223,12297,12392,12490,12564,12644,12745,12798,12884,12950,13039,13129,13191,13255,13318,13392,13504,13614,13724,13829,13888,13943,14256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1916aa273d557d541d17bc9f12ca7920\\transformed\\media3-session-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,139,214,286,352,435,522,618", "endColumns": "83,74,71,65,82,86,95,100", "endOffsets": "134,209,281,347,430,517,613,714"}, "to": {"startLines": "53,62,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3914,4757,9952,10024,10090,10173,10260,10356", "endColumns": "83,74,71,65,82,86,95,100", "endOffsets": "3993,4827,10019,10085,10168,10255,10351,10452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcdce9d9bb74ce2c916a6bbd71ef776f\\transformed\\media3-exoplayer-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6928,7009,7071,7136,7208,7286,7366,7456,7549", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "7004,7066,7131,7203,7281,7361,7451,7544,7618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92121f0061d84fd6603428dd9555221c\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1289,1393,1506,1590,1694,1815,1900,1980,2071,2164,2259,2353,2453,2546,2641,2735,2826,2918,3001,3113,3221,3321,3435,3541,3647,3811,14261", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "1284,1388,1501,1585,1689,1810,1895,1975,2066,2159,2254,2348,2448,2541,2636,2730,2821,2913,2996,3108,3216,3316,3430,3536,3642,3806,3909,14340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78bf313ee09e26c8c671a0bbc2457ec1\\transformed\\media3-ui-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3619,3685,3737,3799,3875,3951", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3614,3680,3732,3794,3870,3946,4003"}, "to": {"startLines": "2,11,16,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,4898,4986,5076,5165,5262,5356,5431,5497,5594,5692,5761,5824,5887,5956,6070,6183,6297,6374,6454,6523,6599,6698,6799,6865,7623,7676,7734,7782,7843,7907,7977,8042,8111,8172,8230,8296,8360,8426,8478,8540,8616,8692", "endLines": "10,15,20,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "376,672,942,4981,5071,5160,5257,5351,5426,5492,5589,5687,5756,5819,5882,5951,6065,6178,6292,6369,6449,6518,6594,6693,6794,6860,6923,7671,7729,7777,7838,7902,7972,8037,8106,8167,8225,8291,8355,8421,8473,8535,8611,8687,8744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f4bdb46ff28ab587a34c53d6687bf99\\transformed\\navigation-ui-2.7.5\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "13948,14058", "endColumns": "109,123", "endOffsets": "14053,14177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c8cb4a267669986cd76225679b8b78b\\transformed\\zxing-android-embedded-4.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,166,275", "endColumns": "58,51,108,116", "endOffsets": "109,161,270,387"}, "to": {"startLines": "184,185,186,187", "startColumns": "4,4,4,4", "startOffsets": "14446,14505,14557,14666", "endColumns": "58,51,108,116", "endOffsets": "14500,14552,14661,14778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48fbfb4201531ba0d2c54a69b6a94add\\transformed\\core-1.9.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "183", "startColumns": "4", "startOffsets": "14345", "endColumns": "100", "endOffsets": "14441"}}]}]}