<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#20000000">
    <item>
        <shape android:shape="rectangle">
            <!-- 设置渐变背景 -->
            <gradient
                android:startColor="@color/sakura_primary"
                android:endColor="@color/sakura_primary_dark"
                android:angle="45" />
            
            <!-- 圆角 -->
            <corners android:radius="8dp" />
            
            <!-- 内边距 -->
            <padding
                android:left="16dp"
                android:top="12dp"
                android:right="16dp"
                android:bottom="12dp" />
        </shape>
    </item>
</ripple> 