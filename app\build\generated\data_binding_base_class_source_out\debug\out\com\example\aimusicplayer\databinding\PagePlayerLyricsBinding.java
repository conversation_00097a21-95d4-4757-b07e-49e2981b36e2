// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.player.LyricView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PagePlayerLyricsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final LyricView lyricsView;

  private PagePlayerLyricsBinding(@NonNull FrameLayout rootView, @NonNull LyricView lyricsView) {
    this.rootView = rootView;
    this.lyricsView = lyricsView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PagePlayerLyricsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PagePlayerLyricsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.page_player_lyrics, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PagePlayerLyricsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.lyrics_view;
      LyricView lyricsView = ViewBindings.findChildViewById(rootView, id);
      if (lyricsView == null) {
        break missingId;
      }

      return new PagePlayerLyricsBinding((FrameLayout) rootView, lyricsView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
