<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="oval">

    <!-- 蓝色渐变背景，正圆形，不贴着容器边框 -->
    <gradient
        android:startColor="#FF4A90E2"
        android:endColor="#FF2E7BD6"
        android:type="radial"
        android:gradientRadius="24dp" />

    <!-- 白色边框 -->
    <stroke
        android:width="2dp"
        android:color="#FFFFFF" />

    <!-- 设置固定尺寸确保正圆形，留出空白不贴边框 -->
    <size
        android:width="48dp"
        android:height="48dp" />

</shape>
