// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUserProfileBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appbarLayout;

  @NonNull
  public final Button btnLogout;

  @NonNull
  public final CardView cardUserStats;

  @NonNull
  public final CollapsingToolbarLayout collapsingToolbar;

  @NonNull
  public final CircleImageView ivUserAvatar;

  @NonNull
  public final ImageView ivUserCover;

  @NonNull
  public final LinearLayout layoutUserInfo;

  @NonNull
  public final LinearLayout layoutUserTags;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvEmail;

  @NonNull
  public final TextView tvError;

  @NonNull
  public final TextView tvFollowedArtistsCount;

  @NonNull
  public final TextView tvLevelTag;

  @NonNull
  public final TextView tvLikedSongsCount;

  @NonNull
  public final TextView tvListeningHours;

  @NonNull
  public final TextView tvMore;

  @NonNull
  public final TextView tvPhone;

  @NonNull
  public final TextView tvPlaylistsCount;

  @NonNull
  public final TextView tvRegisterTime;

  @NonNull
  public final TextView tvSignature;

  @NonNull
  public final TextView tvUsername;

  @NonNull
  public final TextView tvVipStatus;

  @NonNull
  public final TextView tvVipTag;

  @NonNull
  public final View viewGradientOverlay;

  private FragmentUserProfileBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appbarLayout, @NonNull Button btnLogout,
      @NonNull CardView cardUserStats, @NonNull CollapsingToolbarLayout collapsingToolbar,
      @NonNull CircleImageView ivUserAvatar, @NonNull ImageView ivUserCover,
      @NonNull LinearLayout layoutUserInfo, @NonNull LinearLayout layoutUserTags,
      @NonNull ProgressBar progressBar, @NonNull TextView tvEmail, @NonNull TextView tvError,
      @NonNull TextView tvFollowedArtistsCount, @NonNull TextView tvLevelTag,
      @NonNull TextView tvLikedSongsCount, @NonNull TextView tvListeningHours,
      @NonNull TextView tvMore, @NonNull TextView tvPhone, @NonNull TextView tvPlaylistsCount,
      @NonNull TextView tvRegisterTime, @NonNull TextView tvSignature, @NonNull TextView tvUsername,
      @NonNull TextView tvVipStatus, @NonNull TextView tvVipTag,
      @NonNull View viewGradientOverlay) {
    this.rootView = rootView;
    this.appbarLayout = appbarLayout;
    this.btnLogout = btnLogout;
    this.cardUserStats = cardUserStats;
    this.collapsingToolbar = collapsingToolbar;
    this.ivUserAvatar = ivUserAvatar;
    this.ivUserCover = ivUserCover;
    this.layoutUserInfo = layoutUserInfo;
    this.layoutUserTags = layoutUserTags;
    this.progressBar = progressBar;
    this.tvEmail = tvEmail;
    this.tvError = tvError;
    this.tvFollowedArtistsCount = tvFollowedArtistsCount;
    this.tvLevelTag = tvLevelTag;
    this.tvLikedSongsCount = tvLikedSongsCount;
    this.tvListeningHours = tvListeningHours;
    this.tvMore = tvMore;
    this.tvPhone = tvPhone;
    this.tvPlaylistsCount = tvPlaylistsCount;
    this.tvRegisterTime = tvRegisterTime;
    this.tvSignature = tvSignature;
    this.tvUsername = tvUsername;
    this.tvVipStatus = tvVipStatus;
    this.tvVipTag = tvVipTag;
    this.viewGradientOverlay = viewGradientOverlay;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUserProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUserProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_user_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUserProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appbar_layout;
      AppBarLayout appbarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appbarLayout == null) {
        break missingId;
      }

      id = R.id.btn_logout;
      Button btnLogout = ViewBindings.findChildViewById(rootView, id);
      if (btnLogout == null) {
        break missingId;
      }

      id = R.id.card_user_stats;
      CardView cardUserStats = ViewBindings.findChildViewById(rootView, id);
      if (cardUserStats == null) {
        break missingId;
      }

      id = R.id.collapsing_toolbar;
      CollapsingToolbarLayout collapsingToolbar = ViewBindings.findChildViewById(rootView, id);
      if (collapsingToolbar == null) {
        break missingId;
      }

      id = R.id.iv_user_avatar;
      CircleImageView ivUserAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivUserAvatar == null) {
        break missingId;
      }

      id = R.id.iv_user_cover;
      ImageView ivUserCover = ViewBindings.findChildViewById(rootView, id);
      if (ivUserCover == null) {
        break missingId;
      }

      id = R.id.layout_user_info;
      LinearLayout layoutUserInfo = ViewBindings.findChildViewById(rootView, id);
      if (layoutUserInfo == null) {
        break missingId;
      }

      id = R.id.layout_user_tags;
      LinearLayout layoutUserTags = ViewBindings.findChildViewById(rootView, id);
      if (layoutUserTags == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_email;
      TextView tvEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvEmail == null) {
        break missingId;
      }

      id = R.id.tv_error;
      TextView tvError = ViewBindings.findChildViewById(rootView, id);
      if (tvError == null) {
        break missingId;
      }

      id = R.id.tv_followed_artists_count;
      TextView tvFollowedArtistsCount = ViewBindings.findChildViewById(rootView, id);
      if (tvFollowedArtistsCount == null) {
        break missingId;
      }

      id = R.id.tv_level_tag;
      TextView tvLevelTag = ViewBindings.findChildViewById(rootView, id);
      if (tvLevelTag == null) {
        break missingId;
      }

      id = R.id.tv_liked_songs_count;
      TextView tvLikedSongsCount = ViewBindings.findChildViewById(rootView, id);
      if (tvLikedSongsCount == null) {
        break missingId;
      }

      id = R.id.tv_listening_hours;
      TextView tvListeningHours = ViewBindings.findChildViewById(rootView, id);
      if (tvListeningHours == null) {
        break missingId;
      }

      id = R.id.tv_more;
      TextView tvMore = ViewBindings.findChildViewById(rootView, id);
      if (tvMore == null) {
        break missingId;
      }

      id = R.id.tv_phone;
      TextView tvPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvPhone == null) {
        break missingId;
      }

      id = R.id.tv_playlists_count;
      TextView tvPlaylistsCount = ViewBindings.findChildViewById(rootView, id);
      if (tvPlaylistsCount == null) {
        break missingId;
      }

      id = R.id.tv_register_time;
      TextView tvRegisterTime = ViewBindings.findChildViewById(rootView, id);
      if (tvRegisterTime == null) {
        break missingId;
      }

      id = R.id.tv_signature;
      TextView tvSignature = ViewBindings.findChildViewById(rootView, id);
      if (tvSignature == null) {
        break missingId;
      }

      id = R.id.tv_username;
      TextView tvUsername = ViewBindings.findChildViewById(rootView, id);
      if (tvUsername == null) {
        break missingId;
      }

      id = R.id.tv_vip_status;
      TextView tvVipStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvVipStatus == null) {
        break missingId;
      }

      id = R.id.tv_vip_tag;
      TextView tvVipTag = ViewBindings.findChildViewById(rootView, id);
      if (tvVipTag == null) {
        break missingId;
      }

      id = R.id.view_gradient_overlay;
      View viewGradientOverlay = ViewBindings.findChildViewById(rootView, id);
      if (viewGradientOverlay == null) {
        break missingId;
      }

      return new FragmentUserProfileBinding((CoordinatorLayout) rootView, appbarLayout, btnLogout,
          cardUserStats, collapsingToolbar, ivUserAvatar, ivUserCover, layoutUserInfo,
          layoutUserTags, progressBar, tvEmail, tvError, tvFollowedArtistsCount, tvLevelTag,
          tvLikedSongsCount, tvListeningHours, tvMore, tvPhone, tvPlaylistsCount, tvRegisterTime,
          tvSignature, tvUsername, tvVipStatus, tvVipTag, viewGradientOverlay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
