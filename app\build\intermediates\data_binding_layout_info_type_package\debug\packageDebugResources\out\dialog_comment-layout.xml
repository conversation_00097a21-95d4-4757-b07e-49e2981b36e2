<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_comment" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_comment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_comment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="152" endOffset="14"/></Target><Target id="@+id/text_comment_title" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="31"/></Target><Target id="@+id/text_comment_count" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="35" endOffset="33"/></Target><Target id="@+id/button_comment_close" view="ImageButton"><Expressions/><location startLine="37" startOffset="8" endLine="45" endOffset="46"/></Target><Target id="@+id/loading_view_comment" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="49" startOffset="4" endLine="59" endOffset="25"/></Target><Target id="@+id/text_empty_comment" view="TextView"><Expressions/><location startLine="62" startOffset="4" endLine="72" endOffset="33"/></Target><Target id="@+id/swipe_refresh_comment" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="75" startOffset="4" endLine="90" endOffset="59"/></Target><Target id="@+id/recycler_view_comment" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="81" startOffset="8" endLine="89" endOffset="51"/></Target><Target id="@+id/layout_load_more" view="FrameLayout"><Expressions/><location startLine="93" startOffset="4" endLine="105" endOffset="17"/></Target><Target id="@+id/edit_text_comment" view="EditText"><Expressions/><location startLine="115" startOffset="8" endLine="126" endOffset="47"/></Target><Target id="@+id/btn_send_comment" view="Button"><Expressions/><location startLine="128" startOffset="8" endLine="135" endOffset="62"/></Target><Target id="@+id/text_comment_success" view="TextView"><Expressions/><location startLine="139" startOffset="4" endLine="151" endOffset="35"/></Target></Targets></Layout>