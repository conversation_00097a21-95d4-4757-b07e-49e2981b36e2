// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentIntelligenceBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CardView cardCurrentSong;

  @NonNull
  public final ImageView ivAlbumCover;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvIntelligenceSongs;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvArtist;

  @NonNull
  public final TextView tvEmpty;

  @NonNull
  public final TextView tvIntelligenceDesc;

  @NonNull
  public final TextView tvSongTitle;

  private FragmentIntelligenceBinding(@NonNull ConstraintLayout rootView,
      @NonNull CardView cardCurrentSong, @NonNull ImageView ivAlbumCover,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView rvIntelligenceSongs,
      @NonNull Toolbar toolbar, @NonNull TextView tvArtist, @NonNull TextView tvEmpty,
      @NonNull TextView tvIntelligenceDesc, @NonNull TextView tvSongTitle) {
    this.rootView = rootView;
    this.cardCurrentSong = cardCurrentSong;
    this.ivAlbumCover = ivAlbumCover;
    this.progressBar = progressBar;
    this.rvIntelligenceSongs = rvIntelligenceSongs;
    this.toolbar = toolbar;
    this.tvArtist = tvArtist;
    this.tvEmpty = tvEmpty;
    this.tvIntelligenceDesc = tvIntelligenceDesc;
    this.tvSongTitle = tvSongTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentIntelligenceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentIntelligenceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_intelligence, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentIntelligenceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_current_song;
      CardView cardCurrentSong = ViewBindings.findChildViewById(rootView, id);
      if (cardCurrentSong == null) {
        break missingId;
      }

      id = R.id.iv_album_cover;
      ImageView ivAlbumCover = ViewBindings.findChildViewById(rootView, id);
      if (ivAlbumCover == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rv_intelligence_songs;
      RecyclerView rvIntelligenceSongs = ViewBindings.findChildViewById(rootView, id);
      if (rvIntelligenceSongs == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_artist;
      TextView tvArtist = ViewBindings.findChildViewById(rootView, id);
      if (tvArtist == null) {
        break missingId;
      }

      id = R.id.tv_empty;
      TextView tvEmpty = ViewBindings.findChildViewById(rootView, id);
      if (tvEmpty == null) {
        break missingId;
      }

      id = R.id.tv_intelligence_desc;
      TextView tvIntelligenceDesc = ViewBindings.findChildViewById(rootView, id);
      if (tvIntelligenceDesc == null) {
        break missingId;
      }

      id = R.id.tv_song_title;
      TextView tvSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSongTitle == null) {
        break missingId;
      }

      return new FragmentIntelligenceBinding((ConstraintLayout) rootView, cardCurrentSong,
          ivAlbumCover, progressBar, rvIntelligenceSongs, toolbar, tvArtist, tvEmpty,
          tvIntelligenceDesc, tvSongTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
