<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\assets"><file name="auth.properties" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\assets\auth.properties"/><file name="baidu_speech_grammar.bsg" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\assets\baidu_speech_grammar.bsg"/><file name="data_loading.json" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\assets\data_loading.json"/><file name="music_loading.json" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\assets\music_loading.json"/><file name="WakeUp.bin" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\assets\WakeUp.bin"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>