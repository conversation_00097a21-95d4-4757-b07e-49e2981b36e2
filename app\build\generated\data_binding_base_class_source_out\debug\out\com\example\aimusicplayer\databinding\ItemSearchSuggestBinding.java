// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSearchSuggestBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView suggestSubTextView;

  @NonNull
  public final TextView suggestTitleTextView;

  @NonNull
  public final ImageView typeIconView;

  private ItemSearchSuggestBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView suggestSubTextView, @NonNull TextView suggestTitleTextView,
      @NonNull ImageView typeIconView) {
    this.rootView = rootView;
    this.suggestSubTextView = suggestSubTextView;
    this.suggestTitleTextView = suggestTitleTextView;
    this.typeIconView = typeIconView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSearchSuggestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSearchSuggestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_search_suggest, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSearchSuggestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.suggestSubTextView;
      TextView suggestSubTextView = ViewBindings.findChildViewById(rootView, id);
      if (suggestSubTextView == null) {
        break missingId;
      }

      id = R.id.suggestTitleTextView;
      TextView suggestTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (suggestTitleTextView == null) {
        break missingId;
      }

      id = R.id.typeIconView;
      ImageView typeIconView = ViewBindings.findChildViewById(rootView, id);
      if (typeIconView == null) {
        break missingId;
      }

      return new ItemSearchSuggestBinding((ConstraintLayout) rootView, suggestSubTextView,
          suggestTitleTextView, typeIconView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
