package com.example.aimusicplayer.ui.player;

import com.example.aimusicplayer.network.NetworkStateManager;
import com.example.aimusicplayer.utils.AlbumArtCache;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerFragment_MembersInjector implements MembersInjector<PlayerFragment> {
  private final Provider<AlbumArtCache> albumArtCacheProvider;

  private final Provider<NetworkStateManager> networkStateManagerProvider;

  public PlayerFragment_MembersInjector(Provider<AlbumArtCache> albumArtCacheProvider,
      Provider<NetworkStateManager> networkStateManagerProvider) {
    this.albumArtCacheProvider = albumArtCacheProvider;
    this.networkStateManagerProvider = networkStateManagerProvider;
  }

  public static MembersInjector<PlayerFragment> create(
      Provider<AlbumArtCache> albumArtCacheProvider,
      Provider<NetworkStateManager> networkStateManagerProvider) {
    return new PlayerFragment_MembersInjector(albumArtCacheProvider, networkStateManagerProvider);
  }

  @Override
  public void injectMembers(PlayerFragment instance) {
    injectAlbumArtCache(instance, albumArtCacheProvider.get());
    injectNetworkStateManager(instance, networkStateManagerProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.PlayerFragment.albumArtCache")
  public static void injectAlbumArtCache(PlayerFragment instance, AlbumArtCache albumArtCache) {
    instance.albumArtCache = albumArtCache;
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.PlayerFragment.networkStateManager")
  public static void injectNetworkStateManager(PlayerFragment instance,
      NetworkStateManager networkStateManager) {
    instance.networkStateManager = networkStateManager;
  }
}
