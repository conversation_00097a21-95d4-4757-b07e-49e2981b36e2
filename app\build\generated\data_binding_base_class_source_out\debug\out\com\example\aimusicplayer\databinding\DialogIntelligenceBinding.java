// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.widget.LottieLoadingView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogIntelligenceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnCloseIntelligence;

  @NonNull
  public final LottieLoadingView loadingViewIntelligence;

  @NonNull
  public final RecyclerView recyclerViewIntelligence;

  @NonNull
  public final TextView textEmptyIntelligence;

  @NonNull
  public final TextView textIntelligenceTitle;

  private DialogIntelligenceBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnCloseIntelligence, @NonNull LottieLoadingView loadingViewIntelligence,
      @NonNull RecyclerView recyclerViewIntelligence, @NonNull TextView textEmptyIntelligence,
      @NonNull TextView textIntelligenceTitle) {
    this.rootView = rootView;
    this.btnCloseIntelligence = btnCloseIntelligence;
    this.loadingViewIntelligence = loadingViewIntelligence;
    this.recyclerViewIntelligence = recyclerViewIntelligence;
    this.textEmptyIntelligence = textEmptyIntelligence;
    this.textIntelligenceTitle = textIntelligenceTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogIntelligenceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogIntelligenceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_intelligence, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogIntelligenceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_close_intelligence;
      ImageButton btnCloseIntelligence = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseIntelligence == null) {
        break missingId;
      }

      id = R.id.loading_view_intelligence;
      LottieLoadingView loadingViewIntelligence = ViewBindings.findChildViewById(rootView, id);
      if (loadingViewIntelligence == null) {
        break missingId;
      }

      id = R.id.recycler_view_intelligence;
      RecyclerView recyclerViewIntelligence = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewIntelligence == null) {
        break missingId;
      }

      id = R.id.text_empty_intelligence;
      TextView textEmptyIntelligence = ViewBindings.findChildViewById(rootView, id);
      if (textEmptyIntelligence == null) {
        break missingId;
      }

      id = R.id.text_intelligence_title;
      TextView textIntelligenceTitle = ViewBindings.findChildViewById(rootView, id);
      if (textIntelligenceTitle == null) {
        break missingId;
      }

      return new DialogIntelligenceBinding((LinearLayout) rootView, btnCloseIntelligence,
          loadingViewIntelligence, recyclerViewIntelligence, textEmptyIntelligence,
          textIntelligenceTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
