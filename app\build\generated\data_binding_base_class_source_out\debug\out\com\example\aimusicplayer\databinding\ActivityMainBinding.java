// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentContainerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.widget.LottieLoadingView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView btnMenuRight;

  @NonNull
  public final FrameLayout fragmentContainer;

  @NonNull
  public final LottieLoadingView loadingView;

  @NonNull
  public final ImageView navDiscovery;

  @NonNull
  public final View navDiscoveryIndicator;

  @NonNull
  public final ImageView navDriving;

  @NonNull
  public final View navDrivingIndicator;

  @NonNull
  public final FragmentContainerView navHostFragment;

  @NonNull
  public final ImageView navLibrary;

  @NonNull
  public final View navLibraryIndicator;

  @NonNull
  public final ImageView navPlayer;

  @NonNull
  public final View navPlayerIndicator;

  @NonNull
  public final ImageView navProfile;

  @NonNull
  public final View navProfileIndicator;

  @NonNull
  public final ImageView navSettings;

  @NonNull
  public final View navSettingsIndicator;

  @NonNull
  public final LinearLayout sidebarNav;

  private ActivityMainBinding(@NonNull RelativeLayout rootView, @NonNull ImageView btnMenuRight,
      @NonNull FrameLayout fragmentContainer, @NonNull LottieLoadingView loadingView,
      @NonNull ImageView navDiscovery, @NonNull View navDiscoveryIndicator,
      @NonNull ImageView navDriving, @NonNull View navDrivingIndicator,
      @NonNull FragmentContainerView navHostFragment, @NonNull ImageView navLibrary,
      @NonNull View navLibraryIndicator, @NonNull ImageView navPlayer,
      @NonNull View navPlayerIndicator, @NonNull ImageView navProfile,
      @NonNull View navProfileIndicator, @NonNull ImageView navSettings,
      @NonNull View navSettingsIndicator, @NonNull LinearLayout sidebarNav) {
    this.rootView = rootView;
    this.btnMenuRight = btnMenuRight;
    this.fragmentContainer = fragmentContainer;
    this.loadingView = loadingView;
    this.navDiscovery = navDiscovery;
    this.navDiscoveryIndicator = navDiscoveryIndicator;
    this.navDriving = navDriving;
    this.navDrivingIndicator = navDrivingIndicator;
    this.navHostFragment = navHostFragment;
    this.navLibrary = navLibrary;
    this.navLibraryIndicator = navLibraryIndicator;
    this.navPlayer = navPlayer;
    this.navPlayerIndicator = navPlayerIndicator;
    this.navProfile = navProfile;
    this.navProfileIndicator = navProfileIndicator;
    this.navSettings = navSettings;
    this.navSettingsIndicator = navSettingsIndicator;
    this.sidebarNav = sidebarNav;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_menu_right;
      ImageView btnMenuRight = ViewBindings.findChildViewById(rootView, id);
      if (btnMenuRight == null) {
        break missingId;
      }

      id = R.id.fragment_container;
      FrameLayout fragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (fragmentContainer == null) {
        break missingId;
      }

      id = R.id.loading_view;
      LottieLoadingView loadingView = ViewBindings.findChildViewById(rootView, id);
      if (loadingView == null) {
        break missingId;
      }

      id = R.id.nav_discovery;
      ImageView navDiscovery = ViewBindings.findChildViewById(rootView, id);
      if (navDiscovery == null) {
        break missingId;
      }

      id = R.id.nav_discovery_indicator;
      View navDiscoveryIndicator = ViewBindings.findChildViewById(rootView, id);
      if (navDiscoveryIndicator == null) {
        break missingId;
      }

      id = R.id.nav_driving;
      ImageView navDriving = ViewBindings.findChildViewById(rootView, id);
      if (navDriving == null) {
        break missingId;
      }

      id = R.id.nav_driving_indicator;
      View navDrivingIndicator = ViewBindings.findChildViewById(rootView, id);
      if (navDrivingIndicator == null) {
        break missingId;
      }

      id = R.id.nav_host_fragment;
      FragmentContainerView navHostFragment = ViewBindings.findChildViewById(rootView, id);
      if (navHostFragment == null) {
        break missingId;
      }

      id = R.id.nav_library;
      ImageView navLibrary = ViewBindings.findChildViewById(rootView, id);
      if (navLibrary == null) {
        break missingId;
      }

      id = R.id.nav_library_indicator;
      View navLibraryIndicator = ViewBindings.findChildViewById(rootView, id);
      if (navLibraryIndicator == null) {
        break missingId;
      }

      id = R.id.nav_player;
      ImageView navPlayer = ViewBindings.findChildViewById(rootView, id);
      if (navPlayer == null) {
        break missingId;
      }

      id = R.id.nav_player_indicator;
      View navPlayerIndicator = ViewBindings.findChildViewById(rootView, id);
      if (navPlayerIndicator == null) {
        break missingId;
      }

      id = R.id.nav_profile;
      ImageView navProfile = ViewBindings.findChildViewById(rootView, id);
      if (navProfile == null) {
        break missingId;
      }

      id = R.id.nav_profile_indicator;
      View navProfileIndicator = ViewBindings.findChildViewById(rootView, id);
      if (navProfileIndicator == null) {
        break missingId;
      }

      id = R.id.nav_settings;
      ImageView navSettings = ViewBindings.findChildViewById(rootView, id);
      if (navSettings == null) {
        break missingId;
      }

      id = R.id.nav_settings_indicator;
      View navSettingsIndicator = ViewBindings.findChildViewById(rootView, id);
      if (navSettingsIndicator == null) {
        break missingId;
      }

      id = R.id.sidebar_nav;
      LinearLayout sidebarNav = ViewBindings.findChildViewById(rootView, id);
      if (sidebarNav == null) {
        break missingId;
      }

      return new ActivityMainBinding((RelativeLayout) rootView, btnMenuRight, fragmentContainer,
          loadingView, navDiscovery, navDiscoveryIndicator, navDriving, navDrivingIndicator,
          navHostFragment, navLibrary, navLibraryIndicator, navPlayer, navPlayerIndicator,
          navProfile, navProfileIndicator, navSettings, navSettingsIndicator, sidebarNav);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
