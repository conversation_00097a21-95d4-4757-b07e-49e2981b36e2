package com.example.aimusicplayer.ui.intelligence

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Long
import kotlin.jvm.JvmStatic

public data class IntelligenceFragmentArgs(
  public val songId: Long,
  public val playlistId: Long = -1L,
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putLong("songId", this.songId)
    result.putLong("playlistId", this.playlistId)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("songId", this.songId)
    result.set("playlistId", this.playlistId)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): IntelligenceFragmentArgs {
      bundle.setClassLoader(IntelligenceFragmentArgs::class.java.classLoader)
      val __songId : Long
      if (bundle.containsKey("songId")) {
        __songId = bundle.getLong("songId")
      } else {
        throw IllegalArgumentException("Required argument \"songId\" is missing and does not have an android:defaultValue")
      }
      val __playlistId : Long
      if (bundle.containsKey("playlistId")) {
        __playlistId = bundle.getLong("playlistId")
      } else {
        __playlistId = -1L
      }
      return IntelligenceFragmentArgs(__songId, __playlistId)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle): IntelligenceFragmentArgs {
      val __songId : Long?
      if (savedStateHandle.contains("songId")) {
        __songId = savedStateHandle["songId"]
        if (__songId == null) {
          throw IllegalArgumentException("Argument \"songId\" of type long does not support null values")
        }
      } else {
        throw IllegalArgumentException("Required argument \"songId\" is missing and does not have an android:defaultValue")
      }
      val __playlistId : Long?
      if (savedStateHandle.contains("playlistId")) {
        __playlistId = savedStateHandle["playlistId"]
        if (__playlistId == null) {
          throw IllegalArgumentException("Argument \"playlistId\" of type long does not support null values")
        }
      } else {
        __playlistId = -1L
      }
      return IntelligenceFragmentArgs(__songId, __playlistId)
    }
  }
}
