<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="140dp"
    android:height="280dp"
    android:viewportWidth="140"
    android:viewportHeight="280">

    <!-- 唱针底座 - 更立体的设计 -->
    <!-- 底座外圈阴影 -->
    <path
        android:fillColor="#9E9E9E"
        android:fillAlpha="0.3"
        android:pathData="M70,35m-30,0a30,30 0,1 1,60 0a30,30 0,1 1,-60 0" />

    <!-- 底座外圈 -->
    <path
        android:fillColor="#E8E8E8"
        android:pathData="M70,32m-28,0a28,28 0,1 1,56 0a28,28 0,1 1,-56 0" />

    <!-- 底座中圈 -->
    <path
        android:fillColor="#D0D0D0"
        android:pathData="M70,32m-20,0a20,20 0,1 1,40 0a20,20 0,1 1,-40 0" />

    <!-- 底座内圈 -->
    <path
        android:fillColor="#B0B0B0"
        android:pathData="M70,32m-12,0a12,12 0,1 1,24 0a12,12 0,1 1,-24 0" />

    <!-- 底座中心轴 -->
    <path
        android:fillColor="#808080"
        android:pathData="M70,32m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0" />

    <!-- 唱针臂主体 - 更真实的弯曲和立体感 -->
    <path
        android:fillColor="#606060"
        android:pathData="M70,32 Q78,50 85,80 Q92,120 98,160 Q102,200 105,240 L108,240 Q105,200 101,160 Q94,120 87,80 Q80,50 72,32 Z" />

    <!-- 唱针臂侧面高光 -->
    <path
        android:fillColor="#808080"
        android:pathData="M72,32 Q76,50 80,80 Q85,120 90,160 Q93,200 95,240 L98,240 Q95,200 92,160 Q87,120 82,80 Q78,50 74,32 Z" />

    <!-- 唱针臂阴影 -->
    <path
        android:fillColor="#404040"
        android:pathData="M74,32 Q80,50 85,80 Q90,120 95,160 Q98,200 100,240 L102,240 Q99,200 96,160 Q91,120 86,80 Q81,50 75,32 Z" />

    <!-- 唱针头外壳 - 更精细的设计 -->
    <path
        android:fillColor="#707070"
        android:pathData="M100,240 L108,240 L112,250 L110,265 L98,265 L96,250 Z" />

    <!-- 唱针头中层 -->
    <path
        android:fillColor="#606060"
        android:pathData="M102,250 L106,250 L108,260 L100,260 Z" />

    <!-- 唱针头内核 -->
    <path
        android:fillColor="#404040"
        android:pathData="M103,260 L105,260 L104,270 Z" />

    <!-- 高光效果 - 增强立体感 -->
    <!-- 底座高光 -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.7"
        android:pathData="M75,27m-8,0a8,8 0,1 1,16 0a8,8 0,1 1,-16 0" />

    <!-- 臂部高光 -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.4"
        android:pathData="M72,35 Q74,50 76,80 Q78,120 80,160 Q81,200 82,240 L84,240 Q83,200 82,160 Q80,120 78,80 Q76,50 74,35 Z" />

    <!-- 唱针头高光 -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.6"
        android:pathData="M100,245 L102,245 L103,250 L99,250 Z" />

    <!-- 关节连接点 -->
    <path
        android:fillColor="#505050"
        android:pathData="M82,65m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0" />

    <path
        android:fillColor="#505050"
        android:pathData="M92,130m-2.5,0a2.5,2.5 0,1 1,5 0a2.5,2.5 0,1 1,-5 0" />

    <path
        android:fillColor="#505050"
        android:pathData="M98,190m-2,0a2,2 0,1 1,4 0a2,2 0,1 1,-4 0" />

</vector>
