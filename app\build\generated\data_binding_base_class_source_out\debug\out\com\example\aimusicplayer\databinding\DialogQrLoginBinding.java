// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogQrLoginBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnReloadQr;

  @NonNull
  public final FrameLayout qrContainer;

  @NonNull
  public final LinearLayout qrErrorContainer;

  @NonNull
  public final ImageView qrErrorIcon;

  @NonNull
  public final TextView qrErrorText;

  @NonNull
  public final ImageView qrImage;

  @NonNull
  public final ProgressBar qrLoading;

  @NonNull
  public final TextView qrStatus;

  @NonNull
  public final TextView tvSubtitle;

  @NonNull
  public final TextView tvTip;

  @NonNull
  public final TextView tvTitle;

  private DialogQrLoginBinding(@NonNull RelativeLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnReloadQr, @NonNull FrameLayout qrContainer,
      @NonNull LinearLayout qrErrorContainer, @NonNull ImageView qrErrorIcon,
      @NonNull TextView qrErrorText, @NonNull ImageView qrImage, @NonNull ProgressBar qrLoading,
      @NonNull TextView qrStatus, @NonNull TextView tvSubtitle, @NonNull TextView tvTip,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnReloadQr = btnReloadQr;
    this.qrContainer = qrContainer;
    this.qrErrorContainer = qrErrorContainer;
    this.qrErrorIcon = qrErrorIcon;
    this.qrErrorText = qrErrorText;
    this.qrImage = qrImage;
    this.qrLoading = qrLoading;
    this.qrStatus = qrStatus;
    this.tvSubtitle = tvSubtitle;
    this.tvTip = tvTip;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogQrLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogQrLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_qr_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogQrLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_reload_qr;
      Button btnReloadQr = ViewBindings.findChildViewById(rootView, id);
      if (btnReloadQr == null) {
        break missingId;
      }

      id = R.id.qr_container;
      FrameLayout qrContainer = ViewBindings.findChildViewById(rootView, id);
      if (qrContainer == null) {
        break missingId;
      }

      id = R.id.qr_error_container;
      LinearLayout qrErrorContainer = ViewBindings.findChildViewById(rootView, id);
      if (qrErrorContainer == null) {
        break missingId;
      }

      id = R.id.qr_error_icon;
      ImageView qrErrorIcon = ViewBindings.findChildViewById(rootView, id);
      if (qrErrorIcon == null) {
        break missingId;
      }

      id = R.id.qr_error_text;
      TextView qrErrorText = ViewBindings.findChildViewById(rootView, id);
      if (qrErrorText == null) {
        break missingId;
      }

      id = R.id.qr_image;
      ImageView qrImage = ViewBindings.findChildViewById(rootView, id);
      if (qrImage == null) {
        break missingId;
      }

      id = R.id.qr_loading;
      ProgressBar qrLoading = ViewBindings.findChildViewById(rootView, id);
      if (qrLoading == null) {
        break missingId;
      }

      id = R.id.qr_status;
      TextView qrStatus = ViewBindings.findChildViewById(rootView, id);
      if (qrStatus == null) {
        break missingId;
      }

      id = R.id.tv_subtitle;
      TextView tvSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtitle == null) {
        break missingId;
      }

      id = R.id.tv_tip;
      TextView tvTip = ViewBindings.findChildViewById(rootView, id);
      if (tvTip == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogQrLoginBinding((RelativeLayout) rootView, btnCancel, btnReloadQr,
          qrContainer, qrErrorContainer, qrErrorIcon, qrErrorText, qrImage, qrLoading, qrStatus,
          tvSubtitle, tvTip, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
