<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- 设置圆角 -->
    <corners android:radius="16dp" />
    
    <!-- 设置渐变背景 -->
    <gradient
        android:startColor="#FFFFFF"
        android:endColor="#F8F9FA"
        android:angle="135" />
    
    <!-- 设置边框 -->
    <stroke
        android:width="1dp"
        android:color="#DADCE0" />
    
    <!-- 设置内边距 -->
    <padding
        android:left="16dp"
        android:top="16dp"
        android:right="16dp"
        android:bottom="16dp" />
</shape> 