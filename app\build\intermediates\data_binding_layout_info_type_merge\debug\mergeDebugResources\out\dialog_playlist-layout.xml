<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_playlist" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_playlist.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_playlist_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="92" endOffset="14"/></Target><Target id="@+id/text_playlist_count" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="31" endOffset="33"/></Target><Target id="@+id/button_playlist_close" view="ImageButton"><Expressions/><location startLine="33" startOffset="8" endLine="42" endOffset="42"/></Target><Target id="@+id/recycler_view_playlist" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="45" startOffset="4" endLine="50" endOffset="48"/></Target><Target id="@+id/text_empty_playlist" view="TextView"><Expressions/><location startLine="52" startOffset="4" endLine="59" endOffset="35"/></Target><Target id="@+id/button_clear_playlist" view="Button"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="36"/></Target><Target id="@+id/button_shuffle_playlist" view="Button"><Expressions/><location startLine="79" startOffset="8" endLine="88" endOffset="36"/></Target></Targets></Layout>