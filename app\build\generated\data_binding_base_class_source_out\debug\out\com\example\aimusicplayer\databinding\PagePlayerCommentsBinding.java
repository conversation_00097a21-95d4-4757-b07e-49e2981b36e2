// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PagePlayerCommentsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnSendComment;

  @NonNull
  public final EditText editTextComment;

  @NonNull
  public final RecyclerView recyclerViewComment;

  @NonNull
  public final TextView textCommentCount;

  @NonNull
  public final TextView textCommentTitle;

  private PagePlayerCommentsBinding(@NonNull LinearLayout rootView, @NonNull Button btnSendComment,
      @NonNull EditText editTextComment, @NonNull RecyclerView recyclerViewComment,
      @NonNull TextView textCommentCount, @NonNull TextView textCommentTitle) {
    this.rootView = rootView;
    this.btnSendComment = btnSendComment;
    this.editTextComment = editTextComment;
    this.recyclerViewComment = recyclerViewComment;
    this.textCommentCount = textCommentCount;
    this.textCommentTitle = textCommentTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PagePlayerCommentsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PagePlayerCommentsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.page_player_comments, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PagePlayerCommentsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_send_comment;
      Button btnSendComment = ViewBindings.findChildViewById(rootView, id);
      if (btnSendComment == null) {
        break missingId;
      }

      id = R.id.edit_text_comment;
      EditText editTextComment = ViewBindings.findChildViewById(rootView, id);
      if (editTextComment == null) {
        break missingId;
      }

      id = R.id.recycler_view_comment;
      RecyclerView recyclerViewComment = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewComment == null) {
        break missingId;
      }

      id = R.id.text_comment_count;
      TextView textCommentCount = ViewBindings.findChildViewById(rootView, id);
      if (textCommentCount == null) {
        break missingId;
      }

      id = R.id.text_comment_title;
      TextView textCommentTitle = ViewBindings.findChildViewById(rootView, id);
      if (textCommentTitle == null) {
        break missingId;
      }

      return new PagePlayerCommentsBinding((LinearLayout) rootView, btnSendComment, editTextComment,
          recyclerViewComment, textCommentCount, textCommentTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
