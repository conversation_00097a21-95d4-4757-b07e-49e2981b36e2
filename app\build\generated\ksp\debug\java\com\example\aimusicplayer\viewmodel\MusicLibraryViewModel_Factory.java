package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicLibraryViewModel_Factory implements Factory<MusicLibraryViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public MusicLibraryViewModel_Factory(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public MusicLibraryViewModel get() {
    return newInstance(applicationProvider.get(), musicRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static MusicLibraryViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new MusicLibraryViewModel_Factory(applicationProvider, musicRepositoryProvider, errorHandlerProvider);
  }

  public static MusicLibraryViewModel newInstance(Application application,
      MusicRepository musicRepository, GlobalErrorHandler errorHandler) {
    return new MusicLibraryViewModel(application, musicRepository, errorHandler);
  }
}
