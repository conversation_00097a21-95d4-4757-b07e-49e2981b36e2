package com.example.aimusicplayer.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class JavaPlayerControllerWrapper_Factory implements Factory<JavaPlayerControllerWrapper> {
  private final Provider<PlayerController> playerControllerProvider;

  public JavaPlayerControllerWrapper_Factory(Provider<PlayerController> playerControllerProvider) {
    this.playerControllerProvider = playerControllerProvider;
  }

  @Override
  public JavaPlayerControllerWrapper get() {
    return newInstance(playerControllerProvider.get());
  }

  public static JavaPlayerControllerWrapper_Factory create(
      Provider<PlayerController> playerControllerProvider) {
    return new JavaPlayerControllerWrapper_Factory(playerControllerProvider);
  }

  public static JavaPlayerControllerWrapper newInstance(PlayerController playerController) {
    return new JavaPlayerControllerWrapper(playerController);
  }
}
