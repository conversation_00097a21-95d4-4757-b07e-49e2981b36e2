package com.example.aimusicplayer.ui.player

import android.os.Bundle
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections
import com.example.aimusicplayer.R
import kotlin.Int
import kotlin.Long
import kotlin.String

public class PlayerFragmentDirections private constructor() {
  private data class ActionPlayerFragmentToCommentFragment(
    public val songId: Long,
    public val songName: String,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_playerFragment_to_commentFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("songId", this.songId)
        result.putString("songName", this.songName)
        return result
      }
  }

  private data class ActionPlayerFragmentToIntelligenceFragment(
    public val songId: Long,
    public val playlistId: Long = -1L,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_playerFragment_to_intelligenceFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("songId", this.songId)
        result.putLong("playlistId", this.playlistId)
        return result
      }
  }

  public companion object {
    public fun actionPlayerFragmentToPlaylistFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_playerFragment_to_playlistFragment)

    public fun actionPlayerFragmentToCommentFragment(songId: Long, songName: String): NavDirections
        = ActionPlayerFragmentToCommentFragment(songId, songName)

    public fun actionPlayerFragmentToIntelligenceFragment(songId: Long, playlistId: Long = -1L):
        NavDirections = ActionPlayerFragmentToIntelligenceFragment(songId, playlistId)
  }
}
