// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSearchResultBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView albumCover;

  @NonNull
  public final TextView albumName;

  @NonNull
  public final TextView artistName;

  @NonNull
  public final TextView songTitle;

  private ItemSearchResultBinding(@NonNull LinearLayout rootView, @NonNull ImageView albumCover,
      @NonNull TextView albumName, @NonNull TextView artistName, @NonNull TextView songTitle) {
    this.rootView = rootView;
    this.albumCover = albumCover;
    this.albumName = albumName;
    this.artistName = artistName;
    this.songTitle = songTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSearchResultBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSearchResultBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_search_result, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSearchResultBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.album_cover;
      ImageView albumCover = ViewBindings.findChildViewById(rootView, id);
      if (albumCover == null) {
        break missingId;
      }

      id = R.id.album_name;
      TextView albumName = ViewBindings.findChildViewById(rootView, id);
      if (albumName == null) {
        break missingId;
      }

      id = R.id.artist_name;
      TextView artistName = ViewBindings.findChildViewById(rootView, id);
      if (artistName == null) {
        break missingId;
      }

      id = R.id.song_title;
      TextView songTitle = ViewBindings.findChildViewById(rootView, id);
      if (songTitle == null) {
        break missingId;
      }

      return new ItemSearchResultBinding((LinearLayout) rootView, albumCover, albumName, artistName,
          songTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
