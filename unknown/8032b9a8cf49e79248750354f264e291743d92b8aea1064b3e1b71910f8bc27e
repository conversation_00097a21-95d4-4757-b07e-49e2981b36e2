1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aimusicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 百度语音SDK所需权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:5-75
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:22-77
16
17    <!-- Android 13 (API 33) 及以上版本需要区分的存储权限 -->
18    <uses-permission
18-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission
21-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:5-15:38
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:22-77
23        android:maxSdkVersion="32" />
23-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:15:9-35
24
25    <!-- Android 13 新增的精细存储权限 -->
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:5-75
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:5-76
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:5-75
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:22-72
29
30    <!-- 应用所需的其他权限 -->
31    <uses-permission android:name="android.permission.RECORD_AUDIO" />
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:5-70
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:22-68
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:5-78
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:22-76
33    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:5-74
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:22-72
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:5-78
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:22-75
35
36    <!-- 通知权限 (Android 13+) -->
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:5-77
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:22-74
38
39    <!-- 前台服务权限 -->
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:5-77
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:22-74
41
42    <!-- 媒体播放前台服务权限 (Android 14+) -->
43    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:5-92
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:22-89
44
45    <!-- 震动权限 -->
46    <uses-permission android:name="android.permission.VIBRATE" />
46-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:5-66
46-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:22-63
47
48    <!-- 唤醒锁权限 (ExoPlayer播放时保持设备唤醒) -->
49    <uses-permission android:name="android.permission.WAKE_LOCK" />
49-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:41:5-68
49-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:41:22-65
50
51    <!-- Android Automotive支持 -->
52    <uses-feature
52-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:44:5-46:36
53        android:name="android.hardware.type.automotive"
53-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:45:9-56
54        android:required="false" />
54-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:46:9-33
55
56    <!-- 媒体按钮支持 -->
57    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
57-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:49:5-80
57-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:49:22-77
58    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
58-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:5-65
58-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:22-62
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:25:5-27:36
60        android:name="android.hardware.camera"
60-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:26:9-47
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:27:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:28:5-30:36
63        android:name="android.hardware.camera.front"
63-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:29:9-53
64        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
64-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:30:9-33
65    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
66    <uses-feature
66-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:33:5-35:36
67        android:name="android.hardware.camera.autofocus"
67-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:34:9-57
68        android:required="false" />
68-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:35:9-33
69    <uses-feature
69-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:36:5-38:36
70        android:name="android.hardware.camera.flash"
70-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:37:9-53
71        android:required="false" />
71-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:38:9-33
72    <uses-feature
72-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:39:5-41:36
73        android:name="android.hardware.screen.landscape"
73-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:40:9-57
74        android:required="false" />
74-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:41:9-33
75    <uses-feature
75-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:42:5-44:36
76        android:name="android.hardware.wifi"
76-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:43:9-45
77        android:required="false" />
77-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:44:9-33
78
79    <permission
79-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
80        android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
84
85    <application
85-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:51:5-142:19
86        android:name="com.example.aimusicplayer.MusicApplication"
86-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:52:9-41
87        android:allowBackup="true"
87-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:53:9-35
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
89        android:dataExtractionRules="@xml/data_extraction_rules"
89-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:54:9-65
90        android:debuggable="true"
91        android:extractNativeLibs="false"
92        android:fullBackupContent="@xml/backup_rules"
92-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:55:9-54
93        android:hardwareAccelerated="true"
93-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:63:9-43
94        android:icon="@mipmap/ic_launcher"
94-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:56:9-43
95        android:label="轻聆"
95-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:57:9-27
96        android:largeHeap="true"
96-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:64:9-33
97        android:networkSecurityConfig="@xml/network_security_config"
97-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:62:9-69
98        android:requestLegacyExternalStorage="true"
98-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:60:9-52
99        android:supportsRtl="true"
99-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:58:9-35
100        android:testOnly="true"
101        android:theme="@style/Theme.AIMusicPlayer.Automotive"
101-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:59:9-62
102        android:usesCleartextTraffic="true" >
102-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:61:9-44
103
104        <!-- Android Automotive应用元数据 -->
105        <meta-data
105-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:68:9-70:59
106            android:name="com.android.automotive"
106-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:13-50
107            android:resource="@xml/automotive_app_desc" />
107-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:13-56
108
109        <!-- 百度语音SDK必要的meta-data配置 -->
110        <meta-data
110-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:73:9-75:41
111            android:name="com.baidu.speech.APP_ID"
111-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:74:13-51
112            android:value="118558442" />
112-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:75:13-38
113        <meta-data
113-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:76:9-78:56
114            android:name="com.baidu.speech.API_KEY"
114-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:77:13-52
115            android:value="l07tTLiM8XdSVcM6Avmv5FG3" />
115-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:78:13-53
116        <meta-data
116-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:79:9-81:64
117            android:name="com.baidu.speech.SECRET_KEY"
117-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:80:13-55
118            android:value="e4DxN5gewACp162txczyVRuJs4UGBhdb" />
118-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:81:13-61
119
120        <!-- 启动页 -->
121        <activity
121-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:84:9-94:20
122            android:name="com.example.aimusicplayer.ui.splash.SplashActivity"
122-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:85:13-53
123            android:exported="true"
123-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:86:13-36
124            android:hardwareAccelerated="true"
124-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:89:13-47
125            android:screenOrientation="landscape"
125-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:87:13-50
126            android:theme="@style/FullScreenTheme" >
126-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:88:13-51
127            <intent-filter>
127-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:90:13-93:29
128                <action android:name="android.intent.action.MAIN" />
128-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:91:17-69
128-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:91:25-66
129
130                <category android:name="android.intent.category.LAUNCHER" />
130-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:92:17-77
130-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:92:27-74
131            </intent-filter>
132        </activity>
133
134        <!-- 登录页 -->
135        <activity
135-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:97:9-102:50
136            android:name="com.example.aimusicplayer.ui.login.LoginActivity"
136-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:98:13-51
137            android:exported="false"
137-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:99:13-37
138            android:hardwareAccelerated="true"
138-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:102:13-47
139            android:screenOrientation="landscape"
139-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:100:13-50
140            android:theme="@style/FullScreenTheme" />
140-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:101:13-51
141
142        <!-- 主界面（已重命名，移除了MainActivity2） -->
143        <activity
143-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:105:9-110:50
144            android:name="com.example.aimusicplayer.ui.main.MainActivity"
144-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:106:13-49
145            android:exported="false"
145-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:107:13-37
146            android:hardwareAccelerated="true"
146-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:110:13-47
147            android:screenOrientation="landscape"
147-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:108:13-50
148            android:theme="@style/FullScreenTheme" />
148-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:109:13-51
149
150        <!-- 播放服务 - Android Automotive优化 -->
151        <service
151-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:113:9-128:19
152            android:name="com.example.aimusicplayer.service.UnifiedPlaybackService"
152-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:114:13-59
153            android:enabled="true"
153-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:115:13-35
154            android:exported="false"
154-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:116:13-37
155            android:foregroundServiceType="mediaPlayback" >
155-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:117:13-58
156
157            <!-- 媒体会话支持 -->
158            <intent-filter>
158-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:120:13-122:29
159                <action android:name="androidx.media3.session.MediaSessionService" />
159-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:121:17-86
159-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:121:25-83
160            </intent-filter>
161
162            <!-- 媒体按钮支持 -->
163            <intent-filter>
163-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:125:13-127:29
164                <action android:name="android.intent.action.MEDIA_BUTTON" />
164-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:126:17-77
164-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:126:25-74
165            </intent-filter>
166        </service>
167
168        <!-- 播放器页面已迁移到Fragment，不再需要单独的Activity -->
169        <!-- PlayerActivity已删除，功能已迁移到PlayerFragment -->
170
171        <provider
172            android:name="androidx.core.content.FileProvider"
172-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:134:13-62
173            android:authorities="com.example.aimusicplayer.provider"
173-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:135:13-60
174            android:exported="false"
174-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:136:13-37
175            android:grantUriPermissions="true" >
175-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:137:13-47
176            <meta-data
176-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:138:13-140:54
177                android:name="android.support.FILE_PROVIDER_PATHS"
177-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:139:17-67
178                android:resource="@xml/file_paths" />
178-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:140:17-51
179        </provider>
180
181        <activity
181-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
182            android:name="com.karumi.dexter.DexterActivity"
182-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
183            android:theme="@style/Dexter.Internal.Theme.Transparent" />
183-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
184        <activity
184-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:47:9-53:63
185            android:name="com.journeyapps.barcodescanner.CaptureActivity"
185-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:48:13-74
186            android:clearTaskOnLaunch="true"
186-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:49:13-45
187            android:screenOrientation="sensorLandscape"
187-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:50:13-56
188            android:stateNotNeeded="true"
188-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:51:13-42
189            android:theme="@style/zxing_CaptureTheme"
189-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:52:13-54
190            android:windowSoftInputMode="stateAlwaysHidden" />
190-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:53:13-60
191
192        <meta-data
192-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:10:9-12:43
193            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
193-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:11:13-84
194            android:value="GlideModule" />
194-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:12:13-40
195
196        <provider
196-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
197            android:name="androidx.startup.InitializationProvider"
197-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
198            android:authorities="com.example.aimusicplayer.androidx-startup"
198-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
199            android:exported="false" >
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
200            <meta-data
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.emoji2.text.EmojiCompatInitializer"
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
202                android:value="androidx.startup" />
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
204                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
204-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
205                android:value="androidx.startup" />
205-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
206            <meta-data
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
208                android:value="androidx.startup" />
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
209        </provider>
210
211        <uses-library
211-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
212            android:name="androidx.window.extensions"
212-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
213            android:required="false" />
213-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
214        <uses-library
214-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
215            android:name="androidx.window.sidecar"
215-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
216            android:required="false" />
216-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
217
218        <service
218-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
219            android:name="androidx.room.MultiInstanceInvalidationService"
219-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
220            android:directBootAware="true"
220-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
221            android:exported="false" />
221-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
222
223        <receiver
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
224            android:name="androidx.profileinstaller.ProfileInstallReceiver"
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
225            android:directBootAware="false"
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
226            android:enabled="true"
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
227            android:exported="true"
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
228            android:permission="android.permission.DUMP" >
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
230                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
233                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
236                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
237            </intent-filter>
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
239                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
240            </intent-filter>
241        </receiver>
242    </application>
243
244</manifest>
