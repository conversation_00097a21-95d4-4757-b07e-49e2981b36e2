// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView appLogo;

  @NonNull
  public final LinearLayout btnGuestLogin;

  @NonNull
  public final LinearLayout btnPhoneLogin;

  @NonNull
  public final LinearLayout btnQrcodeLogin;

  @NonNull
  public final LinearLayout loginButtonsContainer;

  @NonNull
  public final RelativeLayout loginRootLayout;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvSubtitle;

  @NonNull
  public final TextView tvWelcome;

  private ActivityLoginBinding(@NonNull RelativeLayout rootView, @NonNull ImageView appLogo,
      @NonNull LinearLayout btnGuestLogin, @NonNull LinearLayout btnPhoneLogin,
      @NonNull LinearLayout btnQrcodeLogin, @NonNull LinearLayout loginButtonsContainer,
      @NonNull RelativeLayout loginRootLayout, @NonNull ProgressBar progressBar,
      @NonNull TextView tvStatus, @NonNull TextView tvSubtitle, @NonNull TextView tvWelcome) {
    this.rootView = rootView;
    this.appLogo = appLogo;
    this.btnGuestLogin = btnGuestLogin;
    this.btnPhoneLogin = btnPhoneLogin;
    this.btnQrcodeLogin = btnQrcodeLogin;
    this.loginButtonsContainer = loginButtonsContainer;
    this.loginRootLayout = loginRootLayout;
    this.progressBar = progressBar;
    this.tvStatus = tvStatus;
    this.tvSubtitle = tvSubtitle;
    this.tvWelcome = tvWelcome;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_logo;
      ImageView appLogo = ViewBindings.findChildViewById(rootView, id);
      if (appLogo == null) {
        break missingId;
      }

      id = R.id.btn_guest_login;
      LinearLayout btnGuestLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnGuestLogin == null) {
        break missingId;
      }

      id = R.id.btn_phone_login;
      LinearLayout btnPhoneLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnPhoneLogin == null) {
        break missingId;
      }

      id = R.id.btn_qrcode_login;
      LinearLayout btnQrcodeLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnQrcodeLogin == null) {
        break missingId;
      }

      id = R.id.login_buttons_container;
      LinearLayout loginButtonsContainer = ViewBindings.findChildViewById(rootView, id);
      if (loginButtonsContainer == null) {
        break missingId;
      }

      RelativeLayout loginRootLayout = (RelativeLayout) rootView;

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tv_subtitle;
      TextView tvSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtitle == null) {
        break missingId;
      }

      id = R.id.tv_welcome;
      TextView tvWelcome = ViewBindings.findChildViewById(rootView, id);
      if (tvWelcome == null) {
        break missingId;
      }

      return new ActivityLoginBinding((RelativeLayout) rootView, appLogo, btnGuestLogin,
          btnPhoneLogin, btnQrcodeLogin, loginButtonsContainer, loginRootLayout, progressBar,
          tvStatus, tvSubtitle, tvWelcome);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
