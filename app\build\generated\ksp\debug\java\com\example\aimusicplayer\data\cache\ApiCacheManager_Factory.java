package com.example.aimusicplayer.data.cache;

import com.example.aimusicplayer.data.db.dao.ApiCacheDao;
import com.google.gson.Gson;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ApiCacheManager_Factory implements Factory<ApiCacheManager> {
  private final Provider<ApiCacheDao> apiCacheDaoProvider;

  private final Provider<Gson> gsonProvider;

  public ApiCacheManager_Factory(Provider<ApiCacheDao> apiCacheDaoProvider,
      Provider<Gson> gsonProvider) {
    this.apiCacheDaoProvider = apiCacheDaoProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public ApiCacheManager get() {
    return newInstance(apiCacheDaoProvider.get(), gsonProvider.get());
  }

  public static ApiCacheManager_Factory create(Provider<ApiCacheDao> apiCacheDaoProvider,
      Provider<Gson> gsonProvider) {
    return new ApiCacheManager_Factory(apiCacheDaoProvider, gsonProvider);
  }

  public static ApiCacheManager newInstance(ApiCacheDao apiCacheDao, Gson gson) {
    return new ApiCacheManager(apiCacheDao, gson);
  }
}
