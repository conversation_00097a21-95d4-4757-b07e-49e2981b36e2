// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.widget.LottieLoadingView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCommentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnSendComment;

  @NonNull
  public final ImageButton buttonCommentClose;

  @NonNull
  public final EditText editTextComment;

  @NonNull
  public final FrameLayout layoutLoadMore;

  @NonNull
  public final LottieLoadingView loadingViewComment;

  @NonNull
  public final RecyclerView recyclerViewComment;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshComment;

  @NonNull
  public final TextView textCommentCount;

  @NonNull
  public final TextView textCommentSuccess;

  @NonNull
  public final TextView textCommentTitle;

  @NonNull
  public final TextView textEmptyComment;

  private DialogCommentBinding(@NonNull LinearLayout rootView, @NonNull Button btnSendComment,
      @NonNull ImageButton buttonCommentClose, @NonNull EditText editTextComment,
      @NonNull FrameLayout layoutLoadMore, @NonNull LottieLoadingView loadingViewComment,
      @NonNull RecyclerView recyclerViewComment, @NonNull SwipeRefreshLayout swipeRefreshComment,
      @NonNull TextView textCommentCount, @NonNull TextView textCommentSuccess,
      @NonNull TextView textCommentTitle, @NonNull TextView textEmptyComment) {
    this.rootView = rootView;
    this.btnSendComment = btnSendComment;
    this.buttonCommentClose = buttonCommentClose;
    this.editTextComment = editTextComment;
    this.layoutLoadMore = layoutLoadMore;
    this.loadingViewComment = loadingViewComment;
    this.recyclerViewComment = recyclerViewComment;
    this.swipeRefreshComment = swipeRefreshComment;
    this.textCommentCount = textCommentCount;
    this.textCommentSuccess = textCommentSuccess;
    this.textCommentTitle = textCommentTitle;
    this.textEmptyComment = textEmptyComment;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCommentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCommentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_comment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCommentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_send_comment;
      Button btnSendComment = ViewBindings.findChildViewById(rootView, id);
      if (btnSendComment == null) {
        break missingId;
      }

      id = R.id.button_comment_close;
      ImageButton buttonCommentClose = ViewBindings.findChildViewById(rootView, id);
      if (buttonCommentClose == null) {
        break missingId;
      }

      id = R.id.edit_text_comment;
      EditText editTextComment = ViewBindings.findChildViewById(rootView, id);
      if (editTextComment == null) {
        break missingId;
      }

      id = R.id.layout_load_more;
      FrameLayout layoutLoadMore = ViewBindings.findChildViewById(rootView, id);
      if (layoutLoadMore == null) {
        break missingId;
      }

      id = R.id.loading_view_comment;
      LottieLoadingView loadingViewComment = ViewBindings.findChildViewById(rootView, id);
      if (loadingViewComment == null) {
        break missingId;
      }

      id = R.id.recycler_view_comment;
      RecyclerView recyclerViewComment = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewComment == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_comment;
      SwipeRefreshLayout swipeRefreshComment = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshComment == null) {
        break missingId;
      }

      id = R.id.text_comment_count;
      TextView textCommentCount = ViewBindings.findChildViewById(rootView, id);
      if (textCommentCount == null) {
        break missingId;
      }

      id = R.id.text_comment_success;
      TextView textCommentSuccess = ViewBindings.findChildViewById(rootView, id);
      if (textCommentSuccess == null) {
        break missingId;
      }

      id = R.id.text_comment_title;
      TextView textCommentTitle = ViewBindings.findChildViewById(rootView, id);
      if (textCommentTitle == null) {
        break missingId;
      }

      id = R.id.text_empty_comment;
      TextView textEmptyComment = ViewBindings.findChildViewById(rootView, id);
      if (textEmptyComment == null) {
        break missingId;
      }

      return new DialogCommentBinding((LinearLayout) rootView, btnSendComment, buttonCommentClose,
          editTextComment, layoutLoadMore, loadingViewComment, recyclerViewComment,
          swipeRefreshComment, textCommentCount, textCommentSuccess, textCommentTitle,
          textEmptyComment);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
