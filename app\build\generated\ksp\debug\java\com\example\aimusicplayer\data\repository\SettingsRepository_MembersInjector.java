package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsRepository_MembersInjector implements MembersInjector<SettingsRepository> {
  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  public SettingsRepository_MembersInjector(Provider<ApiCacheManager> apiCacheManagerProvider) {
    this.apiCacheManagerProvider = apiCacheManagerProvider;
  }

  public static MembersInjector<SettingsRepository> create(
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    return new SettingsRepository_MembersInjector(apiCacheManagerProvider);
  }

  @Override
  public void injectMembers(SettingsRepository instance) {
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
  }
}
