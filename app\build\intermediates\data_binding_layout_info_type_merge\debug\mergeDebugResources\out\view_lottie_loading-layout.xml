<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="view_lottie_loading" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\view_lottie_loading.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/view_lottie_loading_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="32" endOffset="14"/></Target><Target id="@+id/lottie_animation_view" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="50"/></Target><Target id="@+id/loading_message" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="30" endOffset="34"/></Target></Targets></Layout>