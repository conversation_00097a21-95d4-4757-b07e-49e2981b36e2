package com.example.aimusicplayer.di;

import com.example.aimusicplayer.data.db.AppDatabase;
import com.example.aimusicplayer.data.db.dao.ApiCacheDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideApiCacheDaoFactory implements Factory<ApiCacheDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideApiCacheDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ApiCacheDao get() {
    return provideApiCacheDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideApiCacheDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideApiCacheDaoFactory(databaseProvider);
  }

  public static ApiCacheDao provideApiCacheDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideApiCacheDao(database));
  }
}
