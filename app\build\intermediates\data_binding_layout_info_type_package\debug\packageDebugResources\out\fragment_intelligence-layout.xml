<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_intelligence" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_intelligence.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_intelligence_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="127" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="43"/></Target><Target id="@+id/card_current_song" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="20" startOffset="4" endLine="87" endOffset="39"/></Target><Target id="@+id/iv_album_cover" view="ImageView"><Expressions/><location startLine="35" startOffset="12" endLine="42" endOffset="57"/></Target><Target id="@+id/tv_song_title" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="57" endOffset="35"/></Target><Target id="@+id/tv_artist" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="72" endOffset="35"/></Target><Target id="@+id/tv_intelligence_desc" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="84" endOffset="74"/></Target><Target id="@+id/rv_intelligence_songs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="90" startOffset="4" endLine="100" endOffset="44"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="103" startOffset="4" endLine="111" endOffset="69"/></Target><Target id="@+id/tv_empty" view="TextView"><Expressions/><location startLine="114" startOffset="4" endLine="125" endOffset="69"/></Target></Targets></Layout>