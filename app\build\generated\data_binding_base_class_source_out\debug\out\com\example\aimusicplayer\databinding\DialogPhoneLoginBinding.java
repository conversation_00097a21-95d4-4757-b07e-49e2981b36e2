// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPhoneLoginBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnGetCaptcha;

  @NonNull
  public final Button btnLogin;

  @NonNull
  public final LinearLayout captchaLoginContainer;

  @NonNull
  public final EditText etCaptcha;

  @NonNull
  public final EditText etPassword;

  @NonNull
  public final EditText etPhone;

  @NonNull
  public final LinearLayout passwordLoginContainer;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvCaptchaLabel;

  @NonNull
  public final TextView tvPasswordLabel;

  @NonNull
  public final TextView tvPhoneLabel;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvSubtitle;

  @NonNull
  public final TextView tvSwitchLoginMethod;

  @NonNull
  public final TextView tvTitle;

  private DialogPhoneLoginBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnGetCaptcha, @NonNull Button btnLogin,
      @NonNull LinearLayout captchaLoginContainer, @NonNull EditText etCaptcha,
      @NonNull EditText etPassword, @NonNull EditText etPhone,
      @NonNull LinearLayout passwordLoginContainer, @NonNull ProgressBar progressBar,
      @NonNull TextView tvCaptchaLabel, @NonNull TextView tvPasswordLabel,
      @NonNull TextView tvPhoneLabel, @NonNull TextView tvStatus, @NonNull TextView tvSubtitle,
      @NonNull TextView tvSwitchLoginMethod, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnGetCaptcha = btnGetCaptcha;
    this.btnLogin = btnLogin;
    this.captchaLoginContainer = captchaLoginContainer;
    this.etCaptcha = etCaptcha;
    this.etPassword = etPassword;
    this.etPhone = etPhone;
    this.passwordLoginContainer = passwordLoginContainer;
    this.progressBar = progressBar;
    this.tvCaptchaLabel = tvCaptchaLabel;
    this.tvPasswordLabel = tvPasswordLabel;
    this.tvPhoneLabel = tvPhoneLabel;
    this.tvStatus = tvStatus;
    this.tvSubtitle = tvSubtitle;
    this.tvSwitchLoginMethod = tvSwitchLoginMethod;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPhoneLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPhoneLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_phone_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPhoneLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_get_captcha;
      Button btnGetCaptcha = ViewBindings.findChildViewById(rootView, id);
      if (btnGetCaptcha == null) {
        break missingId;
      }

      id = R.id.btn_login;
      Button btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.captcha_login_container;
      LinearLayout captchaLoginContainer = ViewBindings.findChildViewById(rootView, id);
      if (captchaLoginContainer == null) {
        break missingId;
      }

      id = R.id.et_captcha;
      EditText etCaptcha = ViewBindings.findChildViewById(rootView, id);
      if (etCaptcha == null) {
        break missingId;
      }

      id = R.id.et_password;
      EditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.et_phone;
      EditText etPhone = ViewBindings.findChildViewById(rootView, id);
      if (etPhone == null) {
        break missingId;
      }

      id = R.id.password_login_container;
      LinearLayout passwordLoginContainer = ViewBindings.findChildViewById(rootView, id);
      if (passwordLoginContainer == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_captcha_label;
      TextView tvCaptchaLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvCaptchaLabel == null) {
        break missingId;
      }

      id = R.id.tv_password_label;
      TextView tvPasswordLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvPasswordLabel == null) {
        break missingId;
      }

      id = R.id.tv_phone_label;
      TextView tvPhoneLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvPhoneLabel == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tv_subtitle;
      TextView tvSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtitle == null) {
        break missingId;
      }

      id = R.id.tv_switch_login_method;
      TextView tvSwitchLoginMethod = ViewBindings.findChildViewById(rootView, id);
      if (tvSwitchLoginMethod == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogPhoneLoginBinding((LinearLayout) rootView, btnCancel, btnGetCaptcha,
          btnLogin, captchaLoginContainer, etCaptcha, etPassword, etPhone, passwordLoginContainer,
          progressBar, tvCaptchaLabel, tvPasswordLabel, tvPhoneLabel, tvStatus, tvSubtitle,
          tvSwitchLoginMethod, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
