package com.example.aimusicplayer.network;

import com.example.aimusicplayer.utils.NetworkStateManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SmartNetworkInterceptor_Factory implements Factory<SmartNetworkInterceptor> {
  private final Provider<NetworkStateManager> networkStateManagerProvider;

  public SmartNetworkInterceptor_Factory(
      Provider<NetworkStateManager> networkStateManagerProvider) {
    this.networkStateManagerProvider = networkStateManagerProvider;
  }

  @Override
  public SmartNetworkInterceptor get() {
    return newInstance(networkStateManagerProvider.get());
  }

  public static SmartNetworkInterceptor_Factory create(
      Provider<NetworkStateManager> networkStateManagerProvider) {
    return new SmartNetworkInterceptor_Factory(networkStateManagerProvider);
  }

  public static SmartNetworkInterceptor newInstance(NetworkStateManager networkStateManager) {
    return new SmartNetworkInterceptor(networkStateManager);
  }
}
