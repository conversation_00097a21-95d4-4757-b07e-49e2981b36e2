// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPlaylistSongBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView imageSongCover;

  @NonNull
  public final TextView textSongArtist;

  @NonNull
  public final TextView textSongTitle;

  private ItemPlaylistSongBinding(@NonNull LinearLayout rootView, @NonNull ImageView imageSongCover,
      @NonNull TextView textSongArtist, @NonNull TextView textSongTitle) {
    this.rootView = rootView;
    this.imageSongCover = imageSongCover;
    this.textSongArtist = textSongArtist;
    this.textSongTitle = textSongTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPlaylistSongBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPlaylistSongBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_playlist_song, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPlaylistSongBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.image_song_cover;
      ImageView imageSongCover = ViewBindings.findChildViewById(rootView, id);
      if (imageSongCover == null) {
        break missingId;
      }

      id = R.id.text_song_artist;
      TextView textSongArtist = ViewBindings.findChildViewById(rootView, id);
      if (textSongArtist == null) {
        break missingId;
      }

      id = R.id.text_song_title;
      TextView textSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (textSongTitle == null) {
        break missingId;
      }

      return new ItemPlaylistSongBinding((LinearLayout) rootView, imageSongCover, textSongArtist,
          textSongTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
