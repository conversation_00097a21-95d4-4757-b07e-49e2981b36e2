<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <!-- 添加歌曲封面 -->
    <ImageView
        android:id="@+id/image_song_cover"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        android:src="@drawable/default_album_art"
        android:contentDescription="歌曲封面" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="12dp">

        <TextView
            android:id="@+id/text_song_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="歌曲标题" />

        <TextView
            android:id="@+id/text_song_artist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_marginTop="4dp"
            tools:text="歌手名称" />
    </LinearLayout>
</LinearLayout>
