<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_playlist" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_playlist.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_playlist_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="59" endOffset="35"/></Target><Target id="@+id/ivPlaylistCover" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="51"/></Target><Target id="@+id/tvPlaylistName" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="37" endOffset="42"/></Target><Target id="@+id/tvPlaylistDescription" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="47" endOffset="41"/></Target><Target id="@+id/tvUpdateFrequency" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="56" endOffset="41"/></Target></Targets></Layout>