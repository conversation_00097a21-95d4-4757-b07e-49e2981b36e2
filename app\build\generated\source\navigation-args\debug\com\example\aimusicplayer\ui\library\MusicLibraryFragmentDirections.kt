package com.example.aimusicplayer.ui.library

import android.os.Bundle
import androidx.navigation.NavDirections
import com.example.aimusicplayer.R
import kotlin.Int
import kotlin.Long

public class MusicLibraryFragmentDirections private constructor() {
  private data class ActionMusicLibraryFragmentToPlayerFragment(
    public val songId: Long = -1L,
    public val playlistId: Long = -1L,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_musicLibraryFragment_to_playerFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("songId", this.songId)
        result.putLong("playlistId", this.playlistId)
        return result
      }
  }

  private data class ActionMusicLibraryFragmentToPlaylistDetailFragment(
    public val playlistId: Long,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_musicLibraryFragment_to_playlistDetailFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("playlistId", this.playlistId)
        return result
      }
  }

  public companion object {
    public fun actionMusicLibraryFragmentToPlayerFragment(songId: Long = -1L, playlistId: Long
        = -1L): NavDirections = ActionMusicLibraryFragmentToPlayerFragment(songId, playlistId)

    public fun actionMusicLibraryFragmentToPlaylistDetailFragment(playlistId: Long): NavDirections =
        ActionMusicLibraryFragmentToPlaylistDetailFragment(playlistId)
  }
}
