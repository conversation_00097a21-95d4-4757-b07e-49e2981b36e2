<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:ordering="sequentially">
    
    <!-- 第一阶段：缩小 -->
    <scale
        android:duration="150"
        android:fromXScale="1.0"
        android:fromYScale="1.0"
        android:pivotX="50%"
        android:pivotY="50%"
        android:toXScale="0.8"
        android:toYScale="0.8"
        android:interpolator="@android:interpolator/accelerate_cubic" />
    
    <!-- 第二阶段：放大并弹出 -->
    <scale
        android:duration="300"
        android:fromXScale="0.8"
        android:fromYScale="0.8"
        android:pivotX="50%"
        android:pivotY="50%"
        android:toXScale="1.1"
        android:toYScale="1.1"
        android:startOffset="150"
        android:interpolator="@android:interpolator/overshoot" />
    
    <!-- 第三阶段：恢复正常大小 -->
    <scale
        android:duration="150"
        android:fromXScale="1.1"
        android:fromYScale="1.1"
        android:pivotX="50%"
        android:pivotY="50%"
        android:toXScale="1.0"
        android:toYScale="1.0"
        android:startOffset="450"
        android:interpolator="@android:interpolator/decelerate_cubic" />
</set>
