// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSongBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView imageSongCover;

  @NonNull
  public final TextView textSongArtist;

  @NonNull
  public final TextView textSongDuration;

  @NonNull
  public final TextView textSongTitle;

  @NonNull
  public final TextView textVipTag;

  private ItemSongBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView imageSongCover,
      @NonNull TextView textSongArtist, @NonNull TextView textSongDuration,
      @NonNull TextView textSongTitle, @NonNull TextView textVipTag) {
    this.rootView = rootView;
    this.imageSongCover = imageSongCover;
    this.textSongArtist = textSongArtist;
    this.textSongDuration = textSongDuration;
    this.textSongTitle = textSongTitle;
    this.textVipTag = textVipTag;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSongBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSongBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_song, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSongBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.image_song_cover;
      ImageView imageSongCover = ViewBindings.findChildViewById(rootView, id);
      if (imageSongCover == null) {
        break missingId;
      }

      id = R.id.text_song_artist;
      TextView textSongArtist = ViewBindings.findChildViewById(rootView, id);
      if (textSongArtist == null) {
        break missingId;
      }

      id = R.id.text_song_duration;
      TextView textSongDuration = ViewBindings.findChildViewById(rootView, id);
      if (textSongDuration == null) {
        break missingId;
      }

      id = R.id.text_song_title;
      TextView textSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (textSongTitle == null) {
        break missingId;
      }

      id = R.id.text_vip_tag;
      TextView textVipTag = ViewBindings.findChildViewById(rootView, id);
      if (textVipTag == null) {
        break missingId;
      }

      return new ItemSongBinding((ConstraintLayout) rootView, imageSongCover, textSongArtist,
          textSongDuration, textSongTitle, textVipTag);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
