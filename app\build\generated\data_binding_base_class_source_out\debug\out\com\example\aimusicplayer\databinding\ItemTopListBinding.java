// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTopListBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView topListCoverImageView;

  @NonNull
  public final TextView topListDescTextView;

  @NonNull
  public final TextView topListNameTextView;

  @NonNull
  public final TextView topListSongCountTextView;

  private ItemTopListBinding(@NonNull CardView rootView, @NonNull ImageView topListCoverImageView,
      @NonNull TextView topListDescTextView, @NonNull TextView topListNameTextView,
      @NonNull TextView topListSongCountTextView) {
    this.rootView = rootView;
    this.topListCoverImageView = topListCoverImageView;
    this.topListDescTextView = topListDescTextView;
    this.topListNameTextView = topListNameTextView;
    this.topListSongCountTextView = topListSongCountTextView;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTopListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTopListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_top_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTopListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.topListCoverImageView;
      ImageView topListCoverImageView = ViewBindings.findChildViewById(rootView, id);
      if (topListCoverImageView == null) {
        break missingId;
      }

      id = R.id.topListDescTextView;
      TextView topListDescTextView = ViewBindings.findChildViewById(rootView, id);
      if (topListDescTextView == null) {
        break missingId;
      }

      id = R.id.topListNameTextView;
      TextView topListNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (topListNameTextView == null) {
        break missingId;
      }

      id = R.id.topListSongCountTextView;
      TextView topListSongCountTextView = ViewBindings.findChildViewById(rootView, id);
      if (topListSongCountTextView == null) {
        break missingId;
      }

      return new ItemTopListBinding((CardView) rootView, topListCoverImageView, topListDescTextView,
          topListNameTextView, topListSongCountTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
