package com.example.aimusicplayer.error;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GlobalErrorHandler_Factory implements Factory<GlobalErrorHandler> {
  private final Provider<Context> contextProvider;

  public GlobalErrorHandler_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GlobalErrorHandler get() {
    return newInstance(contextProvider.get());
  }

  public static GlobalErrorHandler_Factory create(Provider<Context> contextProvider) {
    return new GlobalErrorHandler_Factory(contextProvider);
  }

  public static GlobalErrorHandler newInstance(Context context) {
    return new GlobalErrorHandler(context);
  }
}
