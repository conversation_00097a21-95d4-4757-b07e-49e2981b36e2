// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewAlbumCoverBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageView ivAlbumCover;

  @NonNull
  public final ImageView ivNeedle;

  @NonNull
  public final ImageView ivVinyl;

  private ViewAlbumCoverBinding(@NonNull FrameLayout rootView, @NonNull ImageView ivAlbumCover,
      @NonNull ImageView ivNeedle, @NonNull ImageView ivVinyl) {
    this.rootView = rootView;
    this.ivAlbumCover = ivAlbumCover;
    this.ivNeedle = ivNeedle;
    this.ivVinyl = ivVinyl;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewAlbumCoverBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewAlbumCoverBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_album_cover, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewAlbumCoverBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_album_cover;
      ImageView ivAlbumCover = ViewBindings.findChildViewById(rootView, id);
      if (ivAlbumCover == null) {
        break missingId;
      }

      id = R.id.iv_needle;
      ImageView ivNeedle = ViewBindings.findChildViewById(rootView, id);
      if (ivNeedle == null) {
        break missingId;
      }

      id = R.id.iv_vinyl;
      ImageView ivVinyl = ViewBindings.findChildViewById(rootView, id);
      if (ivVinyl == null) {
        break missingId;
      }

      return new ViewAlbumCoverBinding((FrameLayout) rootView, ivAlbumCover, ivNeedle, ivVinyl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
