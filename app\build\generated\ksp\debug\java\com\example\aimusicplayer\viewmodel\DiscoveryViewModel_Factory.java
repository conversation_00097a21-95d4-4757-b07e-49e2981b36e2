package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DiscoveryViewModel_Factory implements Factory<DiscoveryViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public DiscoveryViewModel_Factory(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public DiscoveryViewModel get() {
    return newInstance(applicationProvider.get(), musicRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static DiscoveryViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new DiscoveryViewModel_Factory(applicationProvider, musicRepositoryProvider, errorHandlerProvider);
  }

  public static DiscoveryViewModel newInstance(Application application,
      MusicRepository musicRepository, GlobalErrorHandler errorHandler) {
    return new DiscoveryViewModel(application, musicRepository, errorHandler);
  }
}
