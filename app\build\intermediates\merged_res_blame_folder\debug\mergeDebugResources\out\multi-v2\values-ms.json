{"logs": [{"outputFile": "com.example.aimusicplayer.app-mergeDebugResources-60:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1916aa273d557d541d17bc9f12ca7920\\transformed\\media3-session-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,275,341,414,490,589", "endColumns": "73,78,66,65,72,75,98,102", "endOffsets": "124,203,270,336,409,485,584,687"}, "to": {"startLines": "50,59,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,4413,9411,9478,9544,9617,9693,9792", "endColumns": "73,78,66,65,72,75,98,102", "endOffsets": "3681,4487,9473,9539,9612,9688,9787,9890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f4bdb46ff28ab587a34c53d6687bf99\\transformed\\navigation-ui-2.7.5\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,112", "endOffsets": "161,274"}, "to": {"startLines": "176,177", "startColumns": "4,4", "startOffsets": "13307,13418", "endColumns": "110,112", "endOffsets": "13413,13526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\83271d0c85512209c52890db5dc246bd\\transformed\\material-1.11.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2838,2926,3004,3057,3108,3174,3245,3323,3406,3485,3558,3634,3707,3778,3885,3977,4050,4140,4233,4307,4378,4469,4521,4601,4669,4753,4838,4900,4964,5027,5099,5203,5311,5407,5513,5570,5625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2833,2921,2999,3052,3103,3169,3240,3318,3401,3480,3553,3629,3702,3773,3880,3972,4045,4135,4228,4302,4373,4464,4516,4596,4664,4748,4833,4895,4959,5022,5094,5198,5306,5402,5508,5565,5620,5706"}, "to": {"startLines": "19,51,52,53,54,55,56,57,58,60,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "738,3686,3766,3845,3932,4024,4111,4214,4330,4492,8269,8362,8427,8486,8573,8635,8697,8757,8823,8885,8939,9047,9104,9165,9220,9291,9895,9986,10072,10220,10306,10392,10520,10608,10686,10739,10790,10856,10927,11005,11088,11167,11240,11316,11389,11460,11567,11659,11732,11822,11915,11989,12060,12151,12203,12283,12351,12435,12520,12582,12646,12709,12781,12885,12993,13089,13195,13252,13531", "endLines": "22,51,52,53,54,55,56,57,58,60,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "912,3761,3840,3927,4019,4106,4209,4325,4408,4552,8357,8422,8481,8568,8630,8692,8752,8818,8880,8934,9042,9099,9160,9215,9286,9406,9981,10067,10215,10301,10387,10515,10603,10681,10734,10785,10851,10922,11000,11083,11162,11235,11311,11384,11455,11562,11654,11727,11817,11910,11984,12055,12146,12198,12278,12346,12430,12515,12577,12641,12704,12776,12880,12988,13084,13190,13247,13302,13612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48fbfb4201531ba0d2c54a69b6a94add\\transformed\\core-1.9.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "180", "startColumns": "4", "startOffsets": "13698", "endColumns": "100", "endOffsets": "13794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78bf313ee09e26c8c671a0bbc2457ec1\\transformed\\media3-ui-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3302,3368,3420,3482,3558,3634", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3297,3363,3415,3477,3553,3629,3691"}, "to": {"startLines": "2,11,15,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,566,4557,4640,4724,4801,4892,4985,5058,5127,5223,5317,5381,5444,5509,5582,5688,5797,5902,5969,6051,6121,6192,6276,6361,6428,7150,7203,7261,7309,7370,7434,7496,7557,7623,7686,7745,7811,7875,7941,7993,8055,8131,8207", "endLines": "10,14,18,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "374,561,733,4635,4719,4796,4887,4980,5053,5122,5218,5312,5376,5439,5504,5577,5683,5792,5897,5964,6046,6116,6187,6271,6356,6423,6486,7198,7256,7304,7365,7429,7491,7552,7618,7681,7740,7806,7870,7936,7988,8050,8126,8202,8264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcdce9d9bb74ce2c916a6bbd71ef776f\\transformed\\media3-exoplayer-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6491,6561,6625,6691,6756,6834,6900,6990,7073", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "6556,6620,6686,6751,6829,6895,6985,7068,7145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92121f0061d84fd6603428dd9555221c\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1028,1133,1241,1328,1432,1543,1622,1700,1791,1884,1979,2073,2171,2264,2359,2453,2544,2635,2715,2827,2935,3032,3141,3245,3352,3511,13617", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "1023,1128,1236,1323,1427,1538,1617,1695,1786,1879,1974,2068,2166,2259,2354,2448,2539,2630,2710,2822,2930,3027,3136,3240,3347,3506,3607,13693"}}]}]}