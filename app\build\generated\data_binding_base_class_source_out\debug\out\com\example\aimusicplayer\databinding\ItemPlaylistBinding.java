// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPlaylistBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivPlaylistCover;

  @NonNull
  public final TextView tvPlaylistDescription;

  @NonNull
  public final TextView tvPlaylistName;

  @NonNull
  public final TextView tvUpdateFrequency;

  private ItemPlaylistBinding(@NonNull CardView rootView, @NonNull ImageView ivPlaylistCover,
      @NonNull TextView tvPlaylistDescription, @NonNull TextView tvPlaylistName,
      @NonNull TextView tvUpdateFrequency) {
    this.rootView = rootView;
    this.ivPlaylistCover = ivPlaylistCover;
    this.tvPlaylistDescription = tvPlaylistDescription;
    this.tvPlaylistName = tvPlaylistName;
    this.tvUpdateFrequency = tvUpdateFrequency;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPlaylistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPlaylistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_playlist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPlaylistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivPlaylistCover;
      ImageView ivPlaylistCover = ViewBindings.findChildViewById(rootView, id);
      if (ivPlaylistCover == null) {
        break missingId;
      }

      id = R.id.tvPlaylistDescription;
      TextView tvPlaylistDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvPlaylistDescription == null) {
        break missingId;
      }

      id = R.id.tvPlaylistName;
      TextView tvPlaylistName = ViewBindings.findChildViewById(rootView, id);
      if (tvPlaylistName == null) {
        break missingId;
      }

      id = R.id.tvUpdateFrequency;
      TextView tvUpdateFrequency = ViewBindings.findChildViewById(rootView, id);
      if (tvUpdateFrequency == null) {
        break missingId;
      }

      return new ItemPlaylistBinding((CardView) rootView, ivPlaylistCover, tvPlaylistDescription,
          tvPlaylistName, tvUpdateFrequency);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
