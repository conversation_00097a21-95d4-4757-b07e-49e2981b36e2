<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_comment" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_comment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_comment_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="146" endOffset="51"/></Target><Target id="@+id/layout_title_bar" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="9" startOffset="4" endLine="45" endOffset="55"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="16" startOffset="8" endLine="27" endOffset="37"/></Target><Target id="@+id/text_comment_title" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="43" endOffset="35"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="48" startOffset="4" endLine="63" endOffset="59"/></Target><Target id="@+id/recycler_view_comments" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="55" startOffset="8" endLine="62" endOffset="51"/></Target><Target id="@+id/load_more_progress" view="ProgressBar"><Expressions/><location startLine="66" startOffset="4" endLine="75" endOffset="36"/></Target><Target id="@+id/text_empty_comment" view="TextView"><Expressions/><location startLine="78" startOffset="4" endLine="90" endOffset="36"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="93" startOffset="4" endLine="102" endOffset="36"/></Target><Target id="@+id/layout_comment_input" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="105" startOffset="4" endLine="144" endOffset="55"/></Target><Target id="@+id/edit_comment" view="EditText"><Expressions/><location startLine="113" startOffset="8" endLine="129" endOffset="55"/></Target><Target id="@+id/btn_send_comment" view="Button"><Expressions/><location startLine="131" startOffset="8" endLine="142" endOffset="55"/></Target></Targets></Layout>