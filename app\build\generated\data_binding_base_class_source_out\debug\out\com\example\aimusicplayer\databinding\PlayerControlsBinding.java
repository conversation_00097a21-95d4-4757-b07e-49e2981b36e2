// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PlayerControlsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView artistTextView;

  @NonNull
  public final TextView currentTimeTextView;

  @NonNull
  public final ImageView nextButton;

  @NonNull
  public final ImageView playPauseButton;

  @NonNull
  public final ImageView previousButton;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final TextView songNameTextView;

  @NonNull
  public final ImageView toggleLyricButton;

  @NonNull
  public final TextView totalTimeTextView;

  private PlayerControlsBinding(@NonNull LinearLayout rootView, @NonNull TextView artistTextView,
      @NonNull TextView currentTimeTextView, @NonNull ImageView nextButton,
      @NonNull ImageView playPauseButton, @NonNull ImageView previousButton,
      @NonNull SeekBar seekBar, @NonNull TextView songNameTextView,
      @NonNull ImageView toggleLyricButton, @NonNull TextView totalTimeTextView) {
    this.rootView = rootView;
    this.artistTextView = artistTextView;
    this.currentTimeTextView = currentTimeTextView;
    this.nextButton = nextButton;
    this.playPauseButton = playPauseButton;
    this.previousButton = previousButton;
    this.seekBar = seekBar;
    this.songNameTextView = songNameTextView;
    this.toggleLyricButton = toggleLyricButton;
    this.totalTimeTextView = totalTimeTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PlayerControlsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PlayerControlsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.player_controls, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PlayerControlsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.artistTextView;
      TextView artistTextView = ViewBindings.findChildViewById(rootView, id);
      if (artistTextView == null) {
        break missingId;
      }

      id = R.id.currentTimeTextView;
      TextView currentTimeTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentTimeTextView == null) {
        break missingId;
      }

      id = R.id.nextButton;
      ImageView nextButton = ViewBindings.findChildViewById(rootView, id);
      if (nextButton == null) {
        break missingId;
      }

      id = R.id.playPauseButton;
      ImageView playPauseButton = ViewBindings.findChildViewById(rootView, id);
      if (playPauseButton == null) {
        break missingId;
      }

      id = R.id.previousButton;
      ImageView previousButton = ViewBindings.findChildViewById(rootView, id);
      if (previousButton == null) {
        break missingId;
      }

      id = R.id.seekBar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.songNameTextView;
      TextView songNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (songNameTextView == null) {
        break missingId;
      }

      id = R.id.toggleLyricButton;
      ImageView toggleLyricButton = ViewBindings.findChildViewById(rootView, id);
      if (toggleLyricButton == null) {
        break missingId;
      }

      id = R.id.totalTimeTextView;
      TextView totalTimeTextView = ViewBindings.findChildViewById(rootView, id);
      if (totalTimeTextView == null) {
        break missingId;
      }

      return new PlayerControlsBinding((LinearLayout) rootView, artistTextView, currentTimeTextView,
          nextButton, playPauseButton, previousButton, seekBar, songNameTextView, toggleLyricButton,
          totalTimeTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
