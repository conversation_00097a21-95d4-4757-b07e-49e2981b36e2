{"logs": [{"outputFile": "com.example.aimusicplayer.app-mergeDebugResources-60:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1916aa273d557d541d17bc9f12ca7920\\transformed\\media3-session-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,203,276,345,425,509,604", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "124,198,271,340,420,504,599,702"}, "to": {"startLines": "56,65,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3981,4782,9898,9971,10040,10120,10204,10299", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "4050,4851,9966,10035,10115,10199,10294,10397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48fbfb4201531ba0d2c54a69b6a94add\\transformed\\core-1.9.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "186", "startColumns": "4", "startOffsets": "14217", "endColumns": "100", "endOffsets": "14313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f4bdb46ff28ab587a34c53d6687bf99\\transformed\\navigation-ui-2.7.5\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "182,183", "startColumns": "4,4", "startOffsets": "13820,13928", "endColumns": "107,126", "endOffsets": "13923,14050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92121f0061d84fd6603428dd9555221c\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1394,1496,1606,1692,1797,1914,1992,2068,2159,2252,2347,2441,2535,2628,2723,2820,2911,3002,3086,3190,3302,3401,3507,3618,3720,3883,14134", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1389,1491,1601,1687,1792,1909,1987,2063,2154,2247,2342,2436,2530,2623,2718,2815,2906,2997,3081,3185,3297,3396,3502,3613,3715,3878,3976,14212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcdce9d9bb74ce2c916a6bbd71ef776f\\transformed\\media3-exoplayer-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6935,7011,7073,7136,7205,7282,7352,7434,7514", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "7006,7068,7131,7200,7277,7347,7429,7509,7589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c8cb4a267669986cd76225679b8b78b\\transformed\\zxing-android-embedded-4.2.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,311", "endColumns": "58,46,149,83", "endOffsets": "109,156,306,390"}, "to": {"startLines": "187,188,189,190", "startColumns": "4,4,4,4", "startOffsets": "14318,14377,14424,14574", "endColumns": "58,46,149,83", "endOffsets": "14372,14419,14569,14653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78bf313ee09e26c8c671a0bbc2457ec1\\transformed\\media3-ui-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,705,4921,5002,5082,5160,5262,5360,5438,5502,5591,5683,5753,5819,5884,5956,6069,6184,6307,6381,6461,6533,6614,6708,6803,6870,7594,7647,7705,7753,7814,7880,7947,8010,8077,8142,8201,8266,8330,8396,8448,8511,8588,8665", "endLines": "10,16,22,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "377,700,1014,4997,5077,5155,5257,5355,5433,5497,5586,5678,5748,5814,5879,5951,6064,6179,6302,6376,6456,6528,6609,6703,6798,6865,6930,7642,7700,7748,7809,7875,7942,8005,8072,8137,8196,8261,8325,8391,8443,8506,8583,8660,8714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\83271d0c85512209c52890db5dc246bd\\transformed\\material-1.11.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2939,3027,3107,3161,3212,3278,3350,3427,3511,3592,3664,3741,3815,3886,3991,4079,4150,4243,4338,4412,4486,4582,4634,4717,4784,4870,4958,5020,5084,5147,5215,5325,5431,5530,5644,5702,5757", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2934,3022,3102,3156,3207,3273,3345,3422,3506,3587,3659,3736,3810,3881,3986,4074,4145,4238,4333,4407,4481,4577,4629,4712,4779,4865,4953,5015,5079,5142,5210,5320,5426,5525,5639,5697,5752,5831"}, "to": {"startLines": "23,57,58,59,60,61,62,63,64,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1019,4055,4133,4211,4288,4391,4483,4575,4701,4856,8719,8818,8894,8955,9044,9108,9175,9229,9297,9357,9411,9528,9588,9650,9704,9776,10402,10486,10578,10715,10793,10875,11002,11090,11170,11224,11275,11341,11413,11490,11574,11655,11727,11804,11878,11949,12054,12142,12213,12306,12401,12475,12549,12645,12697,12780,12847,12933,13021,13083,13147,13210,13278,13388,13494,13593,13707,13765,14055", "endLines": "28,57,58,59,60,61,62,63,64,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,184", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "1282,4128,4206,4283,4386,4478,4570,4696,4777,4916,8813,8889,8950,9039,9103,9170,9224,9292,9352,9406,9523,9583,9645,9699,9771,9893,10481,10573,10710,10788,10870,10997,11085,11165,11219,11270,11336,11408,11485,11569,11650,11722,11799,11873,11944,12049,12137,12208,12301,12396,12470,12544,12640,12692,12775,12842,12928,13016,13078,13142,13205,13273,13383,13489,13588,13702,13760,13815,14129"}}]}]}