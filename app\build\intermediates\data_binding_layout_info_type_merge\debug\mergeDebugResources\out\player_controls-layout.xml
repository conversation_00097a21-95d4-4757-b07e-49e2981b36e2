<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="player_controls" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\player_controls.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/player_controls_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="125" endOffset="14"/></Target><Target id="@+id/songNameTextView" view="TextView"><Expressions/><location startLine="20" startOffset="12" endLine="28" endOffset="42"/></Target><Target id="@+id/artistTextView" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="37" endOffset="41"/></Target><Target id="@+id/toggleLyricButton" view="ImageView"><Expressions/><location startLine="41" startOffset="8" endLine="51" endOffset="58"/></Target><Target id="@+id/currentTimeTextView" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="67" endOffset="37"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="69" startOffset="8" endLine="73" endOffset="39"/></Target><Target id="@+id/totalTimeTextView" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="80" endOffset="37"/></Target><Target id="@+id/previousButton" view="ImageView"><Expressions/><location startLine="90" startOffset="8" endLine="99" endOffset="63"/></Target><Target id="@+id/playPauseButton" view="ImageView"><Expressions/><location startLine="101" startOffset="8" endLine="112" endOffset="59"/></Target><Target id="@+id/nextButton" view="ImageView"><Expressions/><location startLine="114" startOffset="8" endLine="123" endOffset="59"/></Target></Targets></Layout>