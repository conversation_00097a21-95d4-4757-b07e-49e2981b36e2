package com.example.aimusicplayer.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayServiceModule_ProvidePlayerControllerFactory implements Factory<PlayerController> {
  private final Provider<Context> contextProvider;

  public PlayServiceModule_ProvidePlayerControllerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PlayerController get() {
    return providePlayerController(contextProvider.get());
  }

  public static PlayServiceModule_ProvidePlayerControllerFactory create(
      Provider<Context> contextProvider) {
    return new PlayServiceModule_ProvidePlayerControllerFactory(contextProvider);
  }

  public static PlayerController providePlayerController(Context context) {
    return Preconditions.checkNotNullFromProvides(PlayServiceModule.INSTANCE.providePlayerController(context));
  }
}
