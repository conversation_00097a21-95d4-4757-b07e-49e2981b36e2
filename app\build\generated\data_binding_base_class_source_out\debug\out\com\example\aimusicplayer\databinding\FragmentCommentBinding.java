// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCommentBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnSendComment;

  @NonNull
  public final EditText editComment;

  @NonNull
  public final ConstraintLayout layoutCommentInput;

  @NonNull
  public final ConstraintLayout layoutTitleBar;

  @NonNull
  public final ProgressBar loadMoreProgress;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewComments;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final TextView textCommentTitle;

  @NonNull
  public final TextView textEmptyComment;

  private FragmentCommentBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton btnBack,
      @NonNull Button btnSendComment, @NonNull EditText editComment,
      @NonNull ConstraintLayout layoutCommentInput, @NonNull ConstraintLayout layoutTitleBar,
      @NonNull ProgressBar loadMoreProgress, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewComments, @NonNull SwipeRefreshLayout swipeRefreshLayout,
      @NonNull TextView textCommentTitle, @NonNull TextView textEmptyComment) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnSendComment = btnSendComment;
    this.editComment = editComment;
    this.layoutCommentInput = layoutCommentInput;
    this.layoutTitleBar = layoutTitleBar;
    this.loadMoreProgress = loadMoreProgress;
    this.progressBar = progressBar;
    this.recyclerViewComments = recyclerViewComments;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.textCommentTitle = textCommentTitle;
    this.textEmptyComment = textEmptyComment;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCommentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCommentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_comment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCommentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_send_comment;
      Button btnSendComment = ViewBindings.findChildViewById(rootView, id);
      if (btnSendComment == null) {
        break missingId;
      }

      id = R.id.edit_comment;
      EditText editComment = ViewBindings.findChildViewById(rootView, id);
      if (editComment == null) {
        break missingId;
      }

      id = R.id.layout_comment_input;
      ConstraintLayout layoutCommentInput = ViewBindings.findChildViewById(rootView, id);
      if (layoutCommentInput == null) {
        break missingId;
      }

      id = R.id.layout_title_bar;
      ConstraintLayout layoutTitleBar = ViewBindings.findChildViewById(rootView, id);
      if (layoutTitleBar == null) {
        break missingId;
      }

      id = R.id.load_more_progress;
      ProgressBar loadMoreProgress = ViewBindings.findChildViewById(rootView, id);
      if (loadMoreProgress == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_comments;
      RecyclerView recyclerViewComments = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewComments == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.text_comment_title;
      TextView textCommentTitle = ViewBindings.findChildViewById(rootView, id);
      if (textCommentTitle == null) {
        break missingId;
      }

      id = R.id.text_empty_comment;
      TextView textEmptyComment = ViewBindings.findChildViewById(rootView, id);
      if (textEmptyComment == null) {
        break missingId;
      }

      return new FragmentCommentBinding((ConstraintLayout) rootView, btnBack, btnSendComment,
          editComment, layoutCommentInput, layoutTitleBar, loadMoreProgress, progressBar,
          recyclerViewComments, swipeRefreshLayout, textCommentTitle, textEmptyComment);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
