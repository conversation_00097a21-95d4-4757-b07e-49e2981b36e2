package com.example.aimusicplayer.ui.comment

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Long
import kotlin.String
import kotlin.jvm.JvmStatic

public data class CommentFragmentArgs(
  public val songId: Long,
  public val songName: String,
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putLong("songId", this.songId)
    result.putString("songName", this.songName)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("songId", this.songId)
    result.set("songName", this.songName)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): CommentFragmentArgs {
      bundle.setClassLoader(CommentFragmentArgs::class.java.classLoader)
      val __songId : Long
      if (bundle.containsKey("songId")) {
        __songId = bundle.getLong("songId")
      } else {
        throw IllegalArgumentException("Required argument \"songId\" is missing and does not have an android:defaultValue")
      }
      val __songName : String?
      if (bundle.containsKey("songName")) {
        __songName = bundle.getString("songName")
        if (__songName == null) {
          throw IllegalArgumentException("Argument \"songName\" is marked as non-null but was passed a null value.")
        }
      } else {
        throw IllegalArgumentException("Required argument \"songName\" is missing and does not have an android:defaultValue")
      }
      return CommentFragmentArgs(__songId, __songName)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle): CommentFragmentArgs {
      val __songId : Long?
      if (savedStateHandle.contains("songId")) {
        __songId = savedStateHandle["songId"]
        if (__songId == null) {
          throw IllegalArgumentException("Argument \"songId\" of type long does not support null values")
        }
      } else {
        throw IllegalArgumentException("Required argument \"songId\" is missing and does not have an android:defaultValue")
      }
      val __songName : String?
      if (savedStateHandle.contains("songName")) {
        __songName = savedStateHandle["songName"]
        if (__songName == null) {
          throw IllegalArgumentException("Argument \"songName\" is marked as non-null but was passed a null value")
        }
      } else {
        throw IllegalArgumentException("Required argument \"songName\" is missing and does not have an android:defaultValue")
      }
      return CommentFragmentArgs(__songId, __songName)
    }
  }
}
