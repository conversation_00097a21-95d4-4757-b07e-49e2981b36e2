<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/playerFragment">



    <!-- 播放页面 -->
    <fragment
        android:id="@+id/playerFragment"
        android:name="com.example.aimusicplayer.ui.player.PlayerFragment"
        android:label="播放"
        tools:layout="@layout/fragment_player">
        <argument
            android:name="songId"
            app:argType="long"
            android:defaultValue="-1L" />
        <argument
            android:name="playlistId"
            app:argType="long"
            android:defaultValue="-1L" />
        <action
            android:id="@+id/action_playerFragment_to_playlistFragment"
            app:destination="@id/playlistFragment"
            app:enterAnim="@anim/slide_in_up"
            app:exitAnim="@anim/slide_out_down"
            app:popEnterAnim="@anim/slide_in_down"
            app:popExitAnim="@anim/slide_out_up" />
        <action
            android:id="@+id/action_playerFragment_to_commentFragment"
            app:destination="@id/commentFragment"
            app:enterAnim="@anim/slide_in_up"
            app:exitAnim="@anim/slide_out_down"
            app:popEnterAnim="@anim/slide_in_down"
            app:popExitAnim="@anim/slide_out_up" />
        <action
            android:id="@+id/action_playerFragment_to_intelligenceFragment"
            app:destination="@id/intelligenceFragment"
            app:enterAnim="@anim/slide_in_up"
            app:exitAnim="@anim/slide_out_down"
            app:popEnterAnim="@anim/slide_in_down"
            app:popExitAnim="@anim/slide_out_up" />
    </fragment>

    <!-- 我的音乐库 -->
    <fragment
        android:id="@+id/musicLibraryFragment"
        android:name="com.example.aimusicplayer.ui.library.MusicLibraryFragment"
        android:label="音乐库"
        tools:layout="@layout/fragment_music_library">
        <action
            android:id="@+id/action_musicLibraryFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_musicLibraryFragment_to_playlistDetailFragment"
            app:destination="@id/playlistDetailFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <!-- 音乐探索 -->
    <fragment
        android:id="@+id/discoveryFragment"
        android:name="com.example.aimusicplayer.ui.discovery.DiscoveryFragment"
        android:label="发现"
        tools:layout="@layout/fragment_discovery">
        <action
            android:id="@+id/action_discoveryFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_discoveryFragment_to_searchFragment"
            app:destination="@id/searchFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <!-- 驾驶模式 -->
    <fragment
        android:id="@+id/drivingModeFragment"
        android:name="com.example.aimusicplayer.ui.driving.DrivingModeFragment"
        android:label="驾驶模式"
        tools:layout="@layout/fragment_driving_mode" />

    <!-- 设置 -->
    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.example.aimusicplayer.ui.settings.SettingsFragment"
        android:label="设置"
        tools:layout="@layout/fragment_settings" />

    <!-- 播放列表 -->
    <fragment
        android:id="@+id/playlistFragment"
        android:name="com.example.aimusicplayer.ui.playlist.PlaylistFragment"
        android:label="播放列表"
        tools:layout="@layout/fragment_playlist" />

    <!-- 评论页面 -->
    <fragment
        android:id="@+id/commentFragment"
        android:name="com.example.aimusicplayer.ui.comment.CommentFragment"
        android:label="评论"
        tools:layout="@layout/fragment_comment">
        <argument
            android:name="songId"
            app:argType="long" />
        <argument
            android:name="songName"
            app:argType="string" />
        <action
            android:id="@+id/action_commentFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:enterAnim="@anim/slide_in_down"
            app:exitAnim="@anim/slide_out_up"
            app:popEnterAnim="@anim/slide_in_up"
            app:popExitAnim="@anim/slide_out_down" />
    </fragment>

    <!-- 心动模式 -->
    <fragment
        android:id="@+id/intelligenceFragment"
        android:name="com.example.aimusicplayer.ui.intelligence.IntelligenceFragment"
        android:label="心动模式"
        tools:layout="@layout/fragment_intelligence">
        <argument
            android:name="songId"
            app:argType="long" />
        <argument
            android:name="playlistId"
            app:argType="long"
            android:defaultValue="-1L" />
        <action
            android:id="@+id/action_intelligenceFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>



    <!-- 搜索 -->
    <fragment
        android:id="@+id/searchFragment"
        android:name="com.example.aimusicplayer.ui.search.SearchFragment"
        android:label="搜索"
        tools:layout="@layout/fragment_search">
        <argument
            android:name="keyword"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null" />
        <action
            android:id="@+id/action_searchFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <!-- 歌单详情 -->
    <fragment
        android:id="@+id/playlistDetailFragment"
        android:name="com.example.aimusicplayer.ui.playlist.PlaylistDetailFragment"
        android:label="歌单详情"
        tools:layout="@layout/fragment_playlist_detail">
        <argument
            android:name="playlistId"
            app:argType="long" />
        <action
            android:id="@+id/action_playlistDetailFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <!-- 登录 -->
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.example.aimusicplayer.ui.login.LoginFragment"
        android:label="登录"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_playerFragment"
            app:destination="@id/playerFragment"
            app:popUpTo="@id/nav_graph"
            app:popUpToInclusive="true" />
    </fragment>


</navigation>
