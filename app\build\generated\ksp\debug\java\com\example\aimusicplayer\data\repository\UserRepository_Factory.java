package com.example.aimusicplayer.data.repository;

import android.content.Context;
import android.content.SharedPreferences;
import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.data.db.dao.UserDao;
import com.example.aimusicplayer.data.source.ApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepository_Factory implements Factory<UserRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiService> apiServiceProvider;

  private final Provider<SharedPreferences> sharedPreferencesProvider;

  private final Provider<UserDao> userDaoProvider;

  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  public UserRepository_Factory(Provider<Context> contextProvider,
      Provider<ApiService> apiServiceProvider,
      Provider<SharedPreferences> sharedPreferencesProvider, Provider<UserDao> userDaoProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    this.contextProvider = contextProvider;
    this.apiServiceProvider = apiServiceProvider;
    this.sharedPreferencesProvider = sharedPreferencesProvider;
    this.userDaoProvider = userDaoProvider;
    this.apiCacheManagerProvider = apiCacheManagerProvider;
  }

  @Override
  public UserRepository get() {
    UserRepository instance = newInstance(contextProvider.get(), apiServiceProvider.get(), sharedPreferencesProvider.get(), userDaoProvider.get());
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
    return instance;
  }

  public static UserRepository_Factory create(Provider<Context> contextProvider,
      Provider<ApiService> apiServiceProvider,
      Provider<SharedPreferences> sharedPreferencesProvider, Provider<UserDao> userDaoProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    return new UserRepository_Factory(contextProvider, apiServiceProvider, sharedPreferencesProvider, userDaoProvider, apiCacheManagerProvider);
  }

  public static UserRepository newInstance(Context context, ApiService apiService,
      SharedPreferences sharedPreferences, UserDao userDao) {
    return new UserRepository(context, apiService, sharedPreferences, userDao);
  }
}
