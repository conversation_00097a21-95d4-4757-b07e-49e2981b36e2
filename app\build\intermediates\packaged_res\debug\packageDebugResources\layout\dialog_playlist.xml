<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="播放列表"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_playlist_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/text_playlist_title"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:textColor="@color/color_gray_500"
            android:textSize="14sp"
            tools:text="(100首)" />

        <ImageButton
            android:id="@+id/button_playlist_close"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/text_light" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_playlist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="400dp"
        tools:listitem="@layout/item_playlist" />

    <TextView
        android:id="@+id/text_empty_playlist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="播放列表为空"
        android:gravity="center"
        android:padding="16dp"
        android:visibility="gone" />

    <!-- 操作按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/button_clear_playlist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="清空播放列表"
            android:textColor="@color/text_light"
            android:background="@drawable/button_rounded"
            android:padding="12dp" />

        <Button
            android:id="@+id/button_shuffle_playlist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="随机播放"
            android:textColor="@color/text_light"
            android:background="@drawable/button_rounded"
            android:padding="12dp" />

    </LinearLayout>

</LinearLayout>
