<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_heart_mode" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_heart_mode.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_heart_mode_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="114" endOffset="14"/></Target><Target id="@+id/button_heart_mode_refresh" view="ImageButton"><Expressions/><location startLine="31" startOffset="8" endLine="38" endOffset="45"/></Target><Target id="@+id/button_heart_mode_close" view="ImageButton"><Expressions/><location startLine="40" startOffset="8" endLine="47" endOffset="45"/></Target><Target id="@+id/recycler_view_heart_mode" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="73" startOffset="8" endLine="80" endOffset="57"/></Target><Target id="@+id/loading_view_heart_mode" view="FrameLayout"><Expressions/><location startLine="82" startOffset="8" endLine="101" endOffset="21"/></Target><Target id="@+id/btn_start_heart_mode" view="Button"><Expressions/><location startLine="105" startOffset="4" endLine="113" endOffset="33"/></Target></Targets></Layout>