<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs"><file name="bdasr_V3_20210628_cfe8c44.jar" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar"/><file name="com.baidu.tts_2.6.2.2.20200629_44818d4.jar" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar"/></source><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs"><file name="arm64-v8a/libBaiduSpeechSDK.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\arm64-v8a\libBaiduSpeechSDK.so"/><file name="arm64-v8a/libbdEASRAndroid.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\arm64-v8a\libbdEASRAndroid.so"/><file name="arm64-v8a/libBDSpeechDecoder_V1.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\arm64-v8a\libBDSpeechDecoder_V1.so"/><file name="arm64-v8a/libbdSpilWakeup.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\arm64-v8a\libbdSpilWakeup.so"/><file name="arm64-v8a/libbd_easr_s1_merge_normal_20151216.dat.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\arm64-v8a\libbd_easr_s1_merge_normal_20151216.dat.so"/><file name="arm64-v8a/libvad.dnn.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\arm64-v8a\libvad.dnn.so"/><file name="armeabi/libBaiduSpeechSDK.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi\libBaiduSpeechSDK.so"/><file name="armeabi/libbdEASRAndroid.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi\libbdEASRAndroid.so"/><file name="armeabi/libBDSpeechDecoder_V1.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi\libBDSpeechDecoder_V1.so"/><file name="armeabi/libbdSpilWakeup.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi\libbdSpilWakeup.so"/><file name="armeabi/libbd_easr_s1_merge_normal_20151216.dat.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi\libbd_easr_s1_merge_normal_20151216.dat.so"/><file name="armeabi/libvad.dnn.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi\libvad.dnn.so"/><file name="armeabi-v7a/libBaiduSpeechSDK.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi-v7a\libBaiduSpeechSDK.so"/><file name="armeabi-v7a/libbdEASRAndroid.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi-v7a\libbdEASRAndroid.so"/><file name="armeabi-v7a/libBDSpeechDecoder_V1.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi-v7a\libBDSpeechDecoder_V1.so"/><file name="armeabi-v7a/libbdSpilWakeup.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi-v7a\libbdSpilWakeup.so"/><file name="armeabi-v7a/libbd_easr_s1_merge_normal_20151216.dat.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi-v7a\libbd_easr_s1_merge_normal_20151216.dat.so"/><file name="armeabi-v7a/libvad.dnn.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\armeabi-v7a\libvad.dnn.so"/><file name="x86/libBaiduSpeechSDK.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86\libBaiduSpeechSDK.so"/><file name="x86/libbdEASRAndroid.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86\libbdEASRAndroid.so"/><file name="x86/libBDSpeechDecoder_V1.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86\libBDSpeechDecoder_V1.so"/><file name="x86/libbdSpilWakeup.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86\libbdSpilWakeup.so"/><file name="x86/libbd_easr_s1_merge_normal_20151216.dat.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86\libbd_easr_s1_merge_normal_20151216.dat.so"/><file name="x86/libvad.dnn.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86\libvad.dnn.so"/><file name="x86_64/libBaiduSpeechSDK.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86_64\libBaiduSpeechSDK.so"/><file name="x86_64/libbdEASRAndroid.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86_64\libbdEASRAndroid.so"/><file name="x86_64/libBDSpeechDecoder_V1.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86_64\libBDSpeechDecoder_V1.so"/><file name="x86_64/libbdSpilWakeup.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86_64\libbdSpilWakeup.so"/><file name="x86_64/libbd_easr_s1_merge_normal_20151216.dat.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86_64\libbd_easr_s1_merge_normal_20151216.dat.so"/><file name="x86_64/libvad.dnn.so" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\jniLibs\x86_64\libvad.dnn.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\debug\jniLibs"/></dataSet></merger>