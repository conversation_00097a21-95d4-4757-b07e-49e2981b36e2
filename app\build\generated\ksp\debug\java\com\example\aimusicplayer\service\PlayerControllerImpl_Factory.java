package com.example.aimusicplayer.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerControllerImpl_Factory implements Factory<PlayerControllerImpl> {
  private final Provider<Context> contextProvider;

  public PlayerControllerImpl_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PlayerControllerImpl get() {
    return newInstance(contextProvider.get());
  }

  public static PlayerControllerImpl_Factory create(Provider<Context> contextProvider) {
    return new PlayerControllerImpl_Factory(contextProvider);
  }

  public static PlayerControllerImpl newInstance(Context context) {
    return new PlayerControllerImpl(context);
  }
}
