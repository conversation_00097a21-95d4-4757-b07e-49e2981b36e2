package com.example.aimusicplayer.data.repository;

import android.content.SharedPreferences;
import com.example.aimusicplayer.data.cache.ApiCacheManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsRepository_Factory implements Factory<SettingsRepository> {
  private final Provider<SharedPreferences> sharedPreferencesProvider;

  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  public SettingsRepository_Factory(Provider<SharedPreferences> sharedPreferencesProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    this.sharedPreferencesProvider = sharedPreferencesProvider;
    this.apiCacheManagerProvider = apiCacheManagerProvider;
  }

  @Override
  public SettingsRepository get() {
    SettingsRepository instance = newInstance(sharedPreferencesProvider.get());
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
    return instance;
  }

  public static SettingsRepository_Factory create(
      Provider<SharedPreferences> sharedPreferencesProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    return new SettingsRepository_Factory(sharedPreferencesProvider, apiCacheManagerProvider);
  }

  public static SettingsRepository newInstance(SharedPreferences sharedPreferences) {
    return new SettingsRepository(sharedPreferences);
  }
}
