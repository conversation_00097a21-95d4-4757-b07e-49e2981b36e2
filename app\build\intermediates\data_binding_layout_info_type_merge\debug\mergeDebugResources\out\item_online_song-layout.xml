<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_online_song" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_online_song.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_online_song_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="35"/></Target><Target id="@+id/item_song_name" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="31" endOffset="35"/></Target><Target id="@+id/item_vip_tag" view="TextView"><Expressions/><location startLine="33" startOffset="12" endLine="48" endOffset="44"/></Target><Target id="@+id/item_artist_name" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="60" endOffset="31"/></Target><Target id="@+id/item_album_name" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="71" endOffset="31"/></Target></Targets></Layout>