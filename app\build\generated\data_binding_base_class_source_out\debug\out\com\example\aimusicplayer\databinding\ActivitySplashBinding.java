// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final TextView appNameText;

  @NonNull
  public final ImageView splashImageView;

  @NonNull
  public final TextView welcomeText;

  private ActivitySplashBinding(@NonNull RelativeLayout rootView, @NonNull TextView appNameText,
      @NonNull ImageView splashImageView, @NonNull TextView welcomeText) {
    this.rootView = rootView;
    this.appNameText = appNameText;
    this.splashImageView = splashImageView;
    this.welcomeText = welcomeText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_name_text;
      TextView appNameText = ViewBindings.findChildViewById(rootView, id);
      if (appNameText == null) {
        break missingId;
      }

      id = R.id.splashImageView;
      ImageView splashImageView = ViewBindings.findChildViewById(rootView, id);
      if (splashImageView == null) {
        break missingId;
      }

      id = R.id.welcome_text;
      TextView welcomeText = ViewBindings.findChildViewById(rootView, id);
      if (welcomeText == null) {
        break missingId;
      }

      return new ActivitySplashBinding((RelativeLayout) rootView, appNameText, splashImageView,
          welcomeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
