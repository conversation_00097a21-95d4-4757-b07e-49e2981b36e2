// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemHotSearchBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView rankTextView;

  @NonNull
  public final TextView scoreTextView;

  @NonNull
  public final TextView searchWordTextView;

  private ItemHotSearchBinding(@NonNull ConstraintLayout rootView, @NonNull TextView rankTextView,
      @NonNull TextView scoreTextView, @NonNull TextView searchWordTextView) {
    this.rootView = rootView;
    this.rankTextView = rankTextView;
    this.scoreTextView = scoreTextView;
    this.searchWordTextView = searchWordTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemHotSearchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemHotSearchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_hot_search, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemHotSearchBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rankTextView;
      TextView rankTextView = ViewBindings.findChildViewById(rootView, id);
      if (rankTextView == null) {
        break missingId;
      }

      id = R.id.scoreTextView;
      TextView scoreTextView = ViewBindings.findChildViewById(rootView, id);
      if (scoreTextView == null) {
        break missingId;
      }

      id = R.id.searchWordTextView;
      TextView searchWordTextView = ViewBindings.findChildViewById(rootView, id);
      if (searchWordTextView == null) {
        break missingId;
      }

      return new ItemHotSearchBinding((ConstraintLayout) rootView, rankTextView, scoreTextView,
          searchWordTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
