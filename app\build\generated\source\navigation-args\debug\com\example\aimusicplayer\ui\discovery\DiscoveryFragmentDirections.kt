package com.example.aimusicplayer.ui.discovery

import android.os.Bundle
import androidx.navigation.NavDirections
import com.example.aimusicplayer.R
import kotlin.Int
import kotlin.Long
import kotlin.String

public class DiscoveryFragmentDirections private constructor() {
  private data class ActionDiscoveryFragmentToPlayerFragment(
    public val songId: Long = -1L,
    public val playlistId: Long = -1L,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_discoveryFragment_to_playerFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("songId", this.songId)
        result.putLong("playlistId", this.playlistId)
        return result
      }
  }

  private data class ActionDiscoveryFragmentToSearchFragment(
    public val keyword: String? = null,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_discoveryFragment_to_searchFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putString("keyword", this.keyword)
        return result
      }
  }

  public companion object {
    public fun actionDiscoveryFragmentToPlayerFragment(songId: Long = -1L, playlistId: Long = -1L):
        NavDirections = ActionDiscoveryFragmentToPlayerFragment(songId, playlistId)

    public fun actionDiscoveryFragmentToSearchFragment(keyword: String? = null): NavDirections =
        ActionDiscoveryFragmentToSearchFragment(keyword)
  }
}
