// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PagePlayerPlaylistBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView recyclerViewPlaylist;

  @NonNull
  public final TextView textPlaylistCount;

  @NonNull
  public final TextView textPlaylistTitle;

  private PagePlayerPlaylistBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView recyclerViewPlaylist, @NonNull TextView textPlaylistCount,
      @NonNull TextView textPlaylistTitle) {
    this.rootView = rootView;
    this.recyclerViewPlaylist = recyclerViewPlaylist;
    this.textPlaylistCount = textPlaylistCount;
    this.textPlaylistTitle = textPlaylistTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PagePlayerPlaylistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PagePlayerPlaylistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.page_player_playlist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PagePlayerPlaylistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.recycler_view_playlist;
      RecyclerView recyclerViewPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPlaylist == null) {
        break missingId;
      }

      id = R.id.text_playlist_count;
      TextView textPlaylistCount = ViewBindings.findChildViewById(rootView, id);
      if (textPlaylistCount == null) {
        break missingId;
      }

      id = R.id.text_playlist_title;
      TextView textPlaylistTitle = ViewBindings.findChildViewById(rootView, id);
      if (textPlaylistTitle == null) {
        break missingId;
      }

      return new PagePlayerPlaylistBinding((LinearLayout) rootView, recyclerViewPlaylist,
          textPlaylistCount, textPlaylistTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
