package com.example.aimusicplayer.ui.comment

import android.os.Bundle
import androidx.navigation.NavDirections
import com.example.aimusicplayer.R
import kotlin.Int
import kotlin.Long

public class CommentFragmentDirections private constructor() {
  private data class ActionCommentFragmentToPlayerFragment(
    public val songId: Long = -1L,
    public val playlistId: Long = -1L,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_commentFragment_to_playerFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putLong("songId", this.songId)
        result.putLong("playlistId", this.playlistId)
        return result
      }
  }

  public companion object {
    public fun actionCommentFragmentToPlayerFragment(songId: Long = -1L, playlistId: Long = -1L):
        NavDirections = ActionCommentFragmentToPlayerFragment(songId, playlistId)
  }
}
