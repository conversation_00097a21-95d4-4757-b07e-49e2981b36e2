// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogHeartModeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnStartHeartMode;

  @NonNull
  public final ImageButton buttonHeartModeClose;

  @NonNull
  public final ImageButton buttonHeartModeRefresh;

  @NonNull
  public final FrameLayout loadingViewHeartMode;

  @NonNull
  public final RecyclerView recyclerViewHeartMode;

  private DialogHeartModeBinding(@NonNull LinearLayout rootView, @NonNull Button btnStartHeartMode,
      @NonNull ImageButton buttonHeartModeClose, @NonNull ImageButton buttonHeartModeRefresh,
      @NonNull FrameLayout loadingViewHeartMode, @NonNull RecyclerView recyclerViewHeartMode) {
    this.rootView = rootView;
    this.btnStartHeartMode = btnStartHeartMode;
    this.buttonHeartModeClose = buttonHeartModeClose;
    this.buttonHeartModeRefresh = buttonHeartModeRefresh;
    this.loadingViewHeartMode = loadingViewHeartMode;
    this.recyclerViewHeartMode = recyclerViewHeartMode;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogHeartModeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogHeartModeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_heart_mode, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogHeartModeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_start_heart_mode;
      Button btnStartHeartMode = ViewBindings.findChildViewById(rootView, id);
      if (btnStartHeartMode == null) {
        break missingId;
      }

      id = R.id.button_heart_mode_close;
      ImageButton buttonHeartModeClose = ViewBindings.findChildViewById(rootView, id);
      if (buttonHeartModeClose == null) {
        break missingId;
      }

      id = R.id.button_heart_mode_refresh;
      ImageButton buttonHeartModeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (buttonHeartModeRefresh == null) {
        break missingId;
      }

      id = R.id.loading_view_heart_mode;
      FrameLayout loadingViewHeartMode = ViewBindings.findChildViewById(rootView, id);
      if (loadingViewHeartMode == null) {
        break missingId;
      }

      id = R.id.recycler_view_heart_mode;
      RecyclerView recyclerViewHeartMode = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewHeartMode == null) {
        break missingId;
      }

      return new DialogHeartModeBinding((LinearLayout) rootView, btnStartHeartMode,
          buttonHeartModeClose, buttonHeartModeRefresh, loadingViewHeartMode,
          recyclerViewHeartMode);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
