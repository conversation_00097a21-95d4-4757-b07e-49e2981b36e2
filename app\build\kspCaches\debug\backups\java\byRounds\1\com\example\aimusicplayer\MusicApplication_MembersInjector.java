package com.example.aimusicplayer;

import com.example.aimusicplayer.utils.NetworkStateManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicApplication_MembersInjector implements MembersInjector<MusicApplication> {
  private final Provider<NetworkStateManager> networkStateManagerProvider;

  public MusicApplication_MembersInjector(
      Provider<NetworkStateManager> networkStateManagerProvider) {
    this.networkStateManagerProvider = networkStateManagerProvider;
  }

  public static MembersInjector<MusicApplication> create(
      Provider<NetworkStateManager> networkStateManagerProvider) {
    return new MusicApplication_MembersInjector(networkStateManagerProvider);
  }

  @Override
  public void injectMembers(MusicApplication instance) {
    injectNetworkStateManager(instance, networkStateManagerProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.MusicApplication.networkStateManager")
  public static void injectNetworkStateManager(MusicApplication instance,
      NetworkStateManager networkStateManager) {
    instance.networkStateManager = networkStateManager;
  }
}
